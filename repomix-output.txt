This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================
src/
  autoloads/
    input_manager/
      input_manager.gd
      input_manager.gd.uid
      input_manager.tscn
    pigeon/
      message_subscription.gd
      message_subscription.gd.uid
      message.gd
      message.gd.uid
      pigeon.gd
      pigeon.gd.uid
      pigeon.tscn
    scene_manager/
      scene_manager.gd
      scene_manager.gd.uid
      scene_manager.tscn
      scene.gd
      scene.gd.uid
  commands/
    spawn/
      spawn_command.gd
      spawn_command.gd.uid
    base_command.gd
    base_command.gd.uid
  descriptors/
    progress/
      1_level_progress_descriptor.tres
      progress_descriptor.gd
      progress_descriptor.gd.uid
      progress_item_descriptor.gd
      progress_item_descriptor.gd.uid
  messages/
    color/
      color_message_result.gd
      color_message_result.gd.uid
      color_message.gd
      color_message.gd.uid
    player/
      move/
        player_move_message.gd
        player_move_message.gd.uid
        player_stop_message.gd
        player_stop_message.gd.uid
      player_die_message.gd
      player_die_message.gd.uid
      player_paint_fuel_change_message.gd
      player_paint_fuel_change_message.gd.uid
    bounce_message.gd.uid
    cell_color_message.gd
    cell_color_message.gd.uid
    damage_message.gd
    damage_message.gd.uid
    input_message.gd
    input_message.gd.uid
    level_complete_message.gd
    level_complete_message.gd.uid
    level_initialize_message.gd
    level_initialize_message.gd.uid
    paint_fuel_change_message.gd
    paint_fuel_change_message.gd.uid
    paint_fuel_grab_message.gd
    paint_fuel_grab_message.gd.uid
    paint_progress_configuration_message.gd
    paint_progress_configuration_message.gd.uid
    paint_progress_message.gd
    paint_progress_message.gd.uid
    spawn_message.gd
    spawn_message.gd.uid
    switch_scene_message.gd
    switch_scene_message.gd.uid
  resources/
    levels_tileset.tres
  scenes/
    abilities/
      regenerate_on_painted/
        regenerate_on_painted_ability.gd
        regenerate_on_painted_ability.gd.uid
        regenerate_on_painted_ability.tscn
      shield/
        shield_ability.gd
        shield_ability.gd.uid
        shield_ability.tscn
      shield_ability.tscn
    cells/
      base_cell.gd
      base_cell.gd.uid
    color_cell/
      color_cell.gd
      color_cell.gd.uid
      color_cell.tscn
    enemy/
      enemy.gd
      enemy.gd.uid
      enemy.tscn
    game/
      game.gd
      game.gd.uid
      game.tscn
      paint_progress_step.gd
      paint_progress_step.gd.uid
      paint_progress_step.tscn
    game_object_factory/
      game_object_factory.gd
      game_object_factory.gd.uid
      game_object_factory.tscn
    level_selection_menu/
      level_selection_menu.gd
      level_selection_menu.gd.uid
      level_selection_menu.tscn
      start_level_button.gd
      start_level_button.gd.uid
    levels/
      blocking_boxes_level.tscn
      level_4.tscn
      safe_boxes_level.tscn
      shield_level.tscn
    main_menu/
      main_menu.gd
      main_menu.gd.uid
      main_menu.tscn
    paint_fuel/
      paint_fuel.gd
      paint_fuel.gd.uid
      paint_fuel.tscn
    player/
      player.gd
      player.gd.uid
      player.tscn
    water_cell/
      water_cell.gd
      water_cell.gd.uid
      water_cell.tscn
  scripts/
    input/
      keyboard_input_handler.gd
      keyboard_input_handler.gd.uid
      touch_input_handler.gd
      touch_input_handler.gd.uid
  ui/
    hud/
      paint_fuel_status.gd
      paint_fuel_status.gd.uid
      paint_progress.gd
      paint_progress.gd.uid
    lost_menu/
      lost_menu.gd
      lost_menu.gd.uid
    win_menu/
      win_menu.gd
      win_menu.gd.uid
    theme.tres

================================================================
Files
================================================================

================
File: src/scenes/cells/base_cell.gd
================
class_name BaseCell
extends Area2D

var _occupants: Array[Node2D] = []

func _ready() -> void:
	area_entered.connect(_on_occupy)
	area_exited.connect(_on_leave)

func get_occupants() -> Array[Node2D]:
	return _occupants

func on_player_landed(_player: Player) -> void:
	# Этот метод предназначен для переопределения в дочерних классах
	pass

func _on_occupy(occupant: Area2D) -> void:
	if _occupants.size() > 1:
		push_error("Too many occupants in cell: " + str(_occupants.size()))
	_occupants.append(occupant)

func _on_leave(occupant: Area2D) -> void:
	_occupants.erase(occupant)

================
File: src/scenes/cells/base_cell.gd.uid
================
uid://nnasxam2mqbh

================
File: src/scenes/water_cell/water_cell.gd
================
class_name WaterCell
extends BaseCell

func on_player_landed(player: Player) -> void:
	print("Player submerged in water")
	player.set_collision_layer_value(1, false)
	player.set_collision_layer_value(6, true)
	if player.get_paint_fuel() > 0:
		player.decrease_paint_fuel(1)
		print("Player entered water, paint fuel decreased")

================
File: src/scenes/water_cell/water_cell.gd.uid
================
uid://deokxpt1qccat

================
File: src/scenes/water_cell/water_cell.tscn
================
[gd_scene load_steps=5 format=3 uid="uid://bgay07uge8qm"]

[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="1_cc61j"]
[ext_resource type="Script" uid="uid://deokxpt1qccat" path="res://src/scenes/water_cell/water_cell.gd" id="1_vo27h"]

[sub_resource type="AtlasTexture" id="AtlasTexture_vo27h"]
atlas = ExtResource("1_cc61j")
region = Rect2(770, 770, 124, 124)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_46bcd"]
size = Vector2(64, 64)

[node name="WaterCell" type="Area2D"]
collision_layer = 4
script = ExtResource("1_vo27h")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_vo27h")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.5, 1)
shape = SubResource("RectangleShape2D_46bcd")

================
File: src/autoloads/input_manager/input_manager.gd
================
extends Node

var _touch_input: TouchInputHandler
var _analog_input: AnalogInputHandler

func _ready() -> void:
	_touch_input = TouchInputHandler.new()
	_analog_input = AnalogInputHandler.new()

func _input(event: InputEvent) -> void:
	_touch_input.handle(event)
	_analog_input.handle(event)

================
File: src/autoloads/input_manager/input_manager.gd.uid
================
uid://c8t1stldb6yuq

================
File: src/autoloads/pigeon/message_subscription.gd
================
class_name MessageSubscription
extends RefCounted

var event_id: String
var subscriber: Object
var method_to_call: Callable


func _init(id: String, subscriber_: Object, method_to_call_: Callable) -> void:
	self.event_id = id
	self.subscriber = subscriber_
	self.method_to_call = method_to_call_

================
File: src/autoloads/pigeon/message_subscription.gd.uid
================
uid://issdjuiwevfn

================
File: src/autoloads/pigeon/message.gd.uid
================
uid://cnqb4bpnbj3jq

================
File: src/autoloads/pigeon/pigeon.gd.uid
================
uid://da0wbb8gkep1s

================
File: src/autoloads/scene_manager/scene_manager.gd.uid
================
uid://ydma15bvdlbm

================
File: src/autoloads/scene_manager/scene.gd.uid
================
uid://b3lrgj34fm1ew

================
File: src/commands/spawn/spawn_command.gd.uid
================
uid://cig2og2t4c1ja

================
File: src/commands/base_command.gd
================
class_name BaseCommand
extends Resource

func execute() -> void:
	pass

================
File: src/commands/base_command.gd.uid
================
uid://dsrypibfqaej4

================
File: src/descriptors/progress/progress_descriptor.gd.uid
================
uid://dafo2hucd5rft

================
File: src/descriptors/progress/progress_item_descriptor.gd.uid
================
uid://dgea3m7spwfkc

================
File: src/messages/color/color_message_result.gd
================
class_name ColorMessageResult
extends RefCounted

var colored: bool

func _init(colored_: bool) -> void:
	self.colored = colored_

================
File: src/messages/color/color_message_result.gd.uid
================
uid://b7klti3hiw1j5

================
File: src/messages/color/color_message.gd
================
class_name ColorMessage
extends Message

const ID: String = "color"

var color: Color

func _init(color_: Color) -> void:
	super(ID)
	self.color = color_

func set_result(colored: bool) -> void:
	result = ColorMessageResult.new(colored)

================
File: src/messages/color/color_message.gd.uid
================
uid://dam5i14v0hmgv

================
File: src/messages/player/move/player_move_message.gd
================
class_name PlayerMoveMessage
extends Message

const ID: String = "player_move"

func _init() -> void:
	super(ID)

================
File: src/messages/player/move/player_move_message.gd.uid
================
uid://d04v7piosb4uo

================
File: src/messages/player/move/player_stop_message.gd.uid
================
uid://d1f8mi6oodvsi

================
File: src/messages/player/player_die_message.gd
================
class_name PlayerDieMessage
extends Message

const ID: String = "player_die"

func _init() -> void:
	super(ID)

================
File: src/messages/player/player_die_message.gd.uid
================
uid://cyqy8lwuea85b

================
File: src/messages/player/player_paint_fuel_change_message.gd
================
class_name PlayerPaintFuelChangeMessage
extends Message

const ID: String = "player_paint_fuel_change"

var current_fuel: int

func _init(current_fuel_: int) -> void:
	super(ID)
	self.current_fuel = current_fuel_

================
File: src/messages/player/player_paint_fuel_change_message.gd.uid
================
uid://dl8gqey8b1ct7

================
File: src/messages/bounce_message.gd.uid
================
uid://ciwdcoxacvghu

================
File: src/messages/cell_color_message.gd
================
class_name CellColorMessage
extends Message

const ID: String = "cell_color"

var cell: ColorCell

func _init(cell_: ColorCell) -> void:
	super(ID)
	self.cell = cell_

================
File: src/messages/cell_color_message.gd.uid
================
uid://5awf8psqbddc

================
File: src/messages/damage_message.gd.uid
================
uid://gqlb8grn3lk5

================
File: src/messages/input_message.gd
================
class_name InputMessage
extends Message

const ID: String = "input"
enum InputType {
	NONE, # for error handling
	MOVE_UP,
	MOVE_DOWN,
	MOVE_LEFT,
	MOVE_RIGHT
}

var input: InputType

func _init(input_: InputType) -> void:
	super(ID)
	self.input = input_

================
File: src/messages/input_message.gd.uid
================
uid://dlkh24kwa2v57

================
File: src/messages/level_complete_message.gd
================
class_name LevelCompleteMessage
extends Message

const ID: String = "level_complete"

func _init() -> void:
	super(ID)

================
File: src/messages/level_complete_message.gd.uid
================
uid://cpai4tfgj36av

================
File: src/messages/level_initialize_message.gd.uid
================
uid://mysu0i3ojg61

================
File: src/messages/paint_fuel_change_message.gd
================
class_name PaintFuelChangeMessage
extends Message

const ID: String = "paint_fuel_change"
enum Type {
	NONE, # for error handling
	ADD,
	REMOVE
}

var change: Type
var amount: int

func _init(change_: Type, amount_: int) -> void:
	super(ID)
	self.change = change_
	self.amount = amount_

================
File: src/messages/paint_fuel_change_message.gd.uid
================
uid://b8hohx8d3so7o

================
File: src/messages/paint_fuel_grab_message.gd
================
class_name PaintFuelGrabMessage
extends Message

const ID: String = "paint_fuel_grab_message"

func _init() -> void:
	super(ID)

================
File: src/messages/paint_fuel_grab_message.gd.uid
================
uid://b2h8ca03kog3c

================
File: src/messages/paint_progress_configuration_message.gd
================
class_name PaintProgressConfigurationMessage
extends Message

const ID: String = "paint_progress_configuration"

var configuration: Array[ProgressDescriptor]

func _init(configuration_: Array[ProgressDescriptor]) -> void:
	super(ID)
	self.configuration = configuration_

================
File: src/messages/paint_progress_configuration_message.gd.uid
================
uid://de7i4jyrxpxrg

================
File: src/messages/paint_progress_message.gd
================
class_name PaintProgressMessage
extends Message

const ID: String = "paint_progress"

var progress: float

func _init(progress_: float) -> void:
	super(ID)
	self.progress = progress_

================
File: src/messages/paint_progress_message.gd.uid
================
uid://csu05yvdm6wvq

================
File: src/messages/spawn_message.gd.uid
================
uid://b6hpce8gaddkc

================
File: src/messages/switch_scene_message.gd
================
class_name SwitchSceneMessage
extends Message

const ID: String = "switch_scene"

var scene_type: Scene.Type

func _init(type: Scene.Type) -> void:
	super(ID)
	self.scene_type = type

================
File: src/messages/switch_scene_message.gd.uid
================
uid://cwr6gbh8l8rk3

================
File: src/resources/levels_tileset.tres
================
[gd_resource type="TileSet" load_steps=5 format=3 uid="uid://gg1mx8yu85t3"]

[ext_resource type="PackedScene" uid="uid://c2yxxlxf0tepl" path="res://src/scenes/color_cell/color_cell.tscn" id="1_p7hw0"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_komqn"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_xsj47"]
resource_name = "background"
scenes/1/scene = ExtResource("1_p7hw0")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_yxbe0"]
texture = ExtResource("2_komqn")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
12:3/0 = 0
13:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
10:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
0:6/0 = 0
0:6/0/physics_layer_1/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_2/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
12:6/0 = 0
13:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0

[resource]
tile_size = Vector2i(128, 128)
physics_layer_0/collision_layer = 4
physics_layer_0/collision_mask = 0
physics_layer_1/collision_layer = 16
physics_layer_1/collision_mask = 0
physics_layer_2/collision_layer = 32
physics_layer_2/collision_mask = 0
custom_data_layer_0/name = "walkable"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_yxbe0")
sources/0 = SubResource("TileSetScenesCollectionSource_xsj47")

================
File: src/scenes/abilities/regenerate_on_painted/regenerate_on_painted_ability.gd
================
extends Node2D

@export var amount: int

func _ready() -> void:
	Pigeon.subscribe(PlayerStopMessage.ID, self, _on_player_stop)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerStopMessage.ID, self)

func _on_player_stop(msg: PlayerStopMessage) -> void:
	if msg.cell is not ColorCell:
		return
	var color_cell: ColorCell = msg.cell
	if color_cell.colored:
		Pigeon.send(PaintFuelChangeMessage.new(PaintFuelChangeMessage.Type.ADD, amount))

================
File: src/scenes/abilities/regenerate_on_painted/regenerate_on_painted_ability.gd.uid
================
uid://bu7paet67jbei

================
File: src/scenes/abilities/shield/shield_ability.gd.uid
================
uid://qrerfk63fyy2

================
File: src/scenes/abilities/shield/shield_ability.tscn
================
[gd_scene load_steps=2 format=3 uid="uid://de68h5s8qhmno"]

[ext_resource type="Script" uid="uid://qrerfk63fyy2" path="res://src/scenes/abilities/shield/shield_ability.gd" id="1_8heto"]

[node name="ShieldAbility" type="Node2D"]
script = ExtResource("1_8heto")

================
File: src/scenes/abilities/shield_ability.tscn
================
[gd_scene load_steps=2 format=3 uid="uid://7tnwhag4o701"]

[ext_resource type="Script" uid="uid://qrerfk63fyy2" path="res://src/scenes/abilities/shield/shield_ability.gd" id="1_n8a3q"]

[node name="ShieldAbility" type="Node2D"]
script = ExtResource("1_n8a3q")

================
File: src/scenes/color_cell/color_cell.gd.uid
================
uid://c5wff3edrjm3m

================
File: src/scenes/enemy/enemy.gd.uid
================
uid://dnmi8ogm4aiek

================
File: src/scenes/game/game.gd.uid
================
uid://ccs8ekjsp4aii

================
File: src/scenes/game/paint_progress_step.gd
================
class_name PaintProgressStep
extends ColorRect

@export var _preview: TextureRect

#TODO use init() with texture and step_value args
var value: float

func set_preview(texture: Texture2D) -> void:
	_preview.texture = texture

================
File: src/scenes/game/paint_progress_step.gd.uid
================
uid://bdaltdkhaqhot

================
File: src/scenes/game_object_factory/game_object_factory.gd.uid
================
uid://b8tbmn6hfvcl0

================
File: src/scenes/level_selection_menu/level_selection_menu.gd
================
extends Control

================
File: src/scenes/level_selection_menu/level_selection_menu.gd.uid
================
uid://ykpi76ru1lma

================
File: src/scenes/level_selection_menu/start_level_button.gd
================
extends Button

@export var _level_scene: Scene.Type

func _ready() -> void:
	pressed.connect(_on_press)

func _on_press() -> void:
	Pigeon.send(SwitchSceneMessage.new(_level_scene))

================
File: src/scenes/level_selection_menu/start_level_button.gd.uid
================
uid://daxefx2pqod4e

================
File: src/scenes/levels/blocking_boxes_level.tscn
================
[gd_scene load_steps=9 format=4 uid="uid://comjkx4qgwq0m"]

[ext_resource type="PackedScene" uid="uid://c2yxxlxf0tepl" path="res://src/scenes/color_cell/color_cell.tscn" id="1_mgj8a"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_vcgeq"]
[ext_resource type="PackedScene" uid="uid://g3pg4o7n73rs" path="res://src/scenes/player/player.tscn" id="3_bek1j"]
[ext_resource type="PackedScene" uid="uid://c0ryyltbeiocm" path="res://src/scenes/abilities/regenerate_on_painted/regenerate_on_painted_ability.tscn" id="4_6hpvd"]
[ext_resource type="PackedScene" uid="uid://cr1n35atcossq" path="res://src/scenes/game/game.tscn" id="5_2mjrn"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_hk4de"]
resource_name = "background"
scenes/1/scene = ExtResource("1_mgj8a")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_jpxma"]
texture = ExtResource("2_vcgeq")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
12:3/0 = 0
13:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
10:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
0:6/0 = 0
0:6/0/physics_layer_1/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_2/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
12:6/0 = 0
13:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0

[sub_resource type="TileSet" id="TileSet_exbt2"]
tile_size = Vector2i(128, 128)
physics_layer_0/collision_layer = 4
physics_layer_0/collision_mask = 0
physics_layer_1/collision_layer = 16
physics_layer_1/collision_mask = 0
physics_layer_2/collision_layer = 32
physics_layer_2/collision_mask = 0
custom_data_layer_0/name = "walkable"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_jpxma")
sources/0 = SubResource("TileSetScenesCollectionSource_hk4de")

[node name="BlockingBoxesLevel" type="Node2D"]

[node name="Background" type="TileMapLayer" parent="."]
use_parent_material = true
position = Vector2(-100, 48)
tile_map_data = PackedByteArray("AAABAAMAAgADAAAAAFABAAQAAgADAAAAAFABAAUAAgADAAAAAFABAAYAAgADAAAAAFABAAcAAgADAAAAAFABAAgAAgADAAAAAFAIAAMAAgADAAAAAGAIAAQAAgADAAAAAGAIAAUAAgADAAAAAGAIAAYAAgADAAAAAGAIAAcAAgADAAAAAGAIAAgAAgADAAAAAGAHAAIAAgADAAAAADAGAAIAAgADAAAAADAFAAIAAgADAAAAADAEAAIAAgADAAAAADADAAIAAgADAAAAADACAAIAAgADAAAAADABAAIAAgACAAAAADAIAAIAAgACAAAAAGAHAAMAAgADAAYAAAAGAAMAAgADAAYAAAAFAAMAAAAAAAAAAQAEAAMAAAAAAAAAAQADAAMAAAAAAAAAAQACAAMAAAAAAAAAAQACAAQAAAAAAAAAAQADAAQAAAAAAAAAAQAEAAQAAAAAAAAAAQAFAAQAAAAAAAAAAQAGAAQAAgADAAYAAAAHAAQAAgADAAYAAAAHAAUAAAAAAAAAAQAHAAYAAAAAAAAAAQAHAAcAAAAAAAAAAQAHAAgAAAAAAAAAAQAGAAgAAAAAAAAAAQAFAAgAAAAAAAAAAQAEAAgAAAAAAAAAAQADAAgAAAAAAAAAAQACAAgAAAAAAAAAAQACAAcAAAAAAAAAAQADAAcAAAAAAAAAAQAEAAcAAAAAAAAAAQAFAAcAAAAAAAAAAQAGAAcAAAAAAAAAAQAGAAYAAAAAAAAAAQAFAAYAAAAAAAAAAQAEAAYAAAAAAAAAAQADAAYAAAAAAAAAAQACAAYAAAAAAAAAAQACAAUAAAAAAAAAAQADAAUAAAAAAAAAAQAEAAUAAAAAAAAAAQAFAAUAAAAAAAAAAQAGAAUAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAgADAAYAAAACAAwAAgADAAYAAAADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAgADAAYAAAADAAwAAgADAAYAAAAEAAkAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAQAIAAkAAgADAAAAAGAIAAoAAgADAAAAAGAIAAsAAgADAAAAAGAIAAwAAgADAAAAAGABAAkAAgADAAAAAFABAAoAAgADAAAAAFABAAsAAgADAAAAAFABAAwAAgADAAAAAFACAA0AAgADAAAAAAADAA0AAgADAAAAAAAEAA0AAgADAAAAAAAFAA0AAgADAAAAAAAGAA0AAgADAAAAAAAHAA0AAgADAAAAAAAIAA0AAgACAAAAAAABAA0AAgACAAAAAFA=")
tile_set = SubResource("TileSet_exbt2")

[node name="Player" parent="." instance=ExtResource("3_bek1j")]
position = Vector2(220, 495)

[node name="RegenerateOnPaintedAbility" parent="Player" instance=ExtResource("4_6hpvd")]

[node name="Game" parent="." node_paths=PackedStringArray("_player") instance=ExtResource("5_2mjrn")]
_player = NodePath("../Player")

[editable path="Game"]

================
File: src/scenes/levels/level_4.tscn
================
[gd_scene load_steps=9 format=4 uid="uid://dq2kqkegmu2j5"]

[ext_resource type="PackedScene" uid="uid://c2yxxlxf0tepl" path="res://src/scenes/color_cell/color_cell.tscn" id="1_31yrn"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_1ijv4"]
[ext_resource type="PackedScene" uid="uid://bgay07uge8qm" path="res://src/scenes/water_cell/water_cell.tscn" id="2_dwyo6"]
[ext_resource type="PackedScene" uid="uid://g3pg4o7n73rs" path="res://src/scenes/player/player.tscn" id="3_dwyo6"]
[ext_resource type="PackedScene" uid="uid://cr1n35atcossq" path="res://src/scenes/game/game.tscn" id="4_eoeam"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_xsj47"]
resource_name = "background"
scenes/1/scene = ExtResource("1_31yrn")
scenes/2/scene = ExtResource("2_dwyo6")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_yxbe0"]
texture = ExtResource("2_1ijv4")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
12:3/0 = 0
13:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
10:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
0:6/0 = 0
0:6/0/physics_layer_1/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_2/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
12:6/0 = 0
13:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0

[sub_resource type="TileSet" id="TileSet_exbt2"]
tile_size = Vector2i(128, 128)
physics_layer_0/collision_layer = 4
physics_layer_0/collision_mask = 0
physics_layer_1/collision_layer = 16
physics_layer_1/collision_mask = 0
physics_layer_2/collision_layer = 32
physics_layer_2/collision_mask = 0
custom_data_layer_0/name = "walkable"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_yxbe0")
sources/0 = SubResource("TileSetScenesCollectionSource_xsj47")

[node name="Level4" type="Node2D"]

[node name="Background" type="TileMapLayer" parent="."]
use_parent_material = true
position = Vector2(-100, 48)
tile_map_data = PackedByteArray("AAABAAMAAgADAAAAAFABAAQAAgADAAAAAFABAAUAAgADAAAAAFABAAYAAgADAAAAAFABAAcAAgADAAAAAFABAAgAAgADAAAAAFAIAAMAAgADAAAAAGAIAAQAAgADAAAAAGAIAAUAAgADAAAAAGAIAAYAAgADAAAAAGAIAAcAAgADAAAAAGAIAAgAAgADAAAAAGAHAAIAAgADAAAAADAGAAIAAgADAAAAADAFAAIAAgADAAAAADAEAAIAAgADAAAAADADAAIAAgADAAAAADACAAIAAgADAAAAADABAAIAAgACAAAAADAIAAIAAgACAAAAAGAHAAMAAAAAAAAAAQAGAAMAAAAAAAAAAQADAAMAAAAAAAAAAQACAAMAAAAAAAAAAQACAAQAAAAAAAAAAQADAAQAAAAAAAAAAQAGAAQAAAAAAAAAAQAHAAQAAAAAAAAAAQAHAAUAAAAAAAAAAQAHAAYAAAAAAAAAAQAHAAcAAAAAAAAAAQAHAAgAAAAAAAAAAQAGAAgAAAAAAAAAAQAFAAgAAAAAAAAAAQAEAAgAAAAAAAAAAQADAAgAAAAAAAAAAQACAAgAAAAAAAAAAQACAAcAAAAAAAAAAQADAAcAAAAAAAAAAQAEAAcAAAAAAAAAAQAFAAcAAAAAAAAAAQAGAAcAAAAAAAAAAQAGAAYAAAAAAAAAAQAFAAYAAAAAAAAAAQAEAAYAAAAAAAAAAQADAAYAAAAAAAAAAQACAAYAAAAAAAAAAQACAAUAAAAAAAAAAQADAAUAAAAAAAAAAQAGAAUAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAQAIAAkAAgADAAAAAGAIAAoAAgADAAAAAGAIAAsAAgADAAAAAGAIAAwAAgADAAAAAGABAAkAAgADAAAAAFABAAoAAgADAAAAAFABAAsAAgADAAAAAFABAAwAAgADAAAAAFACAA0AAgADAAAAAAADAA0AAgADAAAAAAAEAA0AAgADAAAAAAAFAA0AAgADAAAAAAAGAA0AAgADAAAAAAAHAA0AAgADAAAAAAAIAA0AAgACAAAAAAABAA0AAgACAAAAAFAEAAMAAAAAAAAAAgAFAAMAAAAAAAAAAgAFAAQAAAAAAAAAAgAEAAQAAAAAAAAAAgAEAAUAAAAAAAAAAgAFAAUAAAAAAAAAAgA=")
tile_set = SubResource("TileSet_exbt2")

[node name="Player" parent="." instance=ExtResource("3_dwyo6")]
position = Vector2(220, 495)

[node name="Game" parent="." node_paths=PackedStringArray("_player") instance=ExtResource("4_eoeam")]
_player = NodePath("../Player")

[node name="PaintFuel" parent="Game" index="1"]
position = Vector2(732, 624)

[editable path="Game"]

================
File: src/scenes/levels/safe_boxes_level.tscn
================
[gd_scene load_steps=8 format=4 uid="uid://cssnyxomo54kq"]

[ext_resource type="PackedScene" uid="uid://c2yxxlxf0tepl" path="res://src/scenes/color_cell/color_cell.tscn" id="1_pdy1w"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_17avx"]
[ext_resource type="PackedScene" uid="uid://g3pg4o7n73rs" path="res://src/scenes/player/player.tscn" id="3_2e3f2"]
[ext_resource type="PackedScene" uid="uid://cr1n35atcossq" path="res://src/scenes/game/game.tscn" id="4_3kdfk"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_xsj47"]
resource_name = "background"
scenes/1/scene = ExtResource("1_pdy1w")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_yxbe0"]
texture = ExtResource("2_17avx")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
12:3/0 = 0
13:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
10:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
0:6/0 = 0
0:6/0/physics_layer_1/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_2/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
12:6/0 = 0
13:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0

[sub_resource type="TileSet" id="TileSet_exbt2"]
tile_size = Vector2i(128, 128)
physics_layer_0/collision_layer = 4
physics_layer_0/collision_mask = 0
physics_layer_1/collision_layer = 16
physics_layer_1/collision_mask = 0
physics_layer_2/collision_layer = 32
physics_layer_2/collision_mask = 0
custom_data_layer_0/name = "walkable"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_yxbe0")
sources/0 = SubResource("TileSetScenesCollectionSource_xsj47")

[node name="SafeBoxesLevel" type="Node2D"]

[node name="Background" type="TileMapLayer" parent="."]
use_parent_material = true
position = Vector2(-100, 48)
tile_map_data = PackedByteArray("AAABAAMAAgADAAAAAFABAAQAAgADAAAAAFABAAUAAgADAAAAAFABAAYAAgADAAAAAFABAAcAAgADAAAAAFABAAgAAgADAAAAAFAIAAMAAgADAAAAAGAIAAQAAgADAAAAAGAIAAUAAgADAAAAAGAIAAYAAgADAAAAAGAIAAcAAgADAAAAAGAIAAgAAgADAAAAAGAHAAIAAgADAAAAADAGAAIAAgADAAAAADAFAAIAAgADAAAAADAEAAIAAgADAAAAADADAAIAAgADAAAAADACAAIAAgADAAAAADABAAIAAgACAAAAADAIAAIAAgACAAAAAGAHAAMAAAAAAAAAAQAGAAMAAAAAAAAAAQAFAAMAAAAAAAAAAQAEAAMAAAAAAAAAAQADAAMAAAAAAAAAAQACAAMAAAAAAAAAAQACAAQAAAAAAAAAAQADAAQAAAAAAAAAAQAEAAQAAAAAAAAAAQAFAAQAAAAAAAAAAQAGAAQAAAAAAAAAAQAHAAQAAAAAAAAAAQAHAAUAAAAAAAAAAQAHAAYAAAAAAAAAAQAHAAcAAAAAAAAAAQAHAAgAAAAAAAAAAQAGAAgAAAAAAAAAAQAFAAgAAAAAAAAAAQAEAAgAAAAAAAAAAQADAAgAAAAAAAAAAQACAAgAAAAAAAAAAQACAAcAAAAAAAAAAQADAAcAAAAAAAAAAQAEAAcAAAAAAAAAAQAFAAcAAAAAAAAAAQAGAAcAAAAAAAAAAQAGAAYAAAAAAAAAAQAFAAYAAgAAAAYAAAAEAAYAAgAAAAYAAAADAAYAAAAAAAAAAQACAAYAAAAAAAAAAQACAAUAAAAAAAAAAQADAAUAAAAAAAAAAQAEAAUAAgAAAAYAAAAFAAUAAgAAAAYAAAAGAAUAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQAEAAkAAgAAAAYAAAAEAAoAAgAAAAYAAAAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAFAAkAAgAAAAYAAAAFAAoAAgAAAAYAAAAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAQAIAAkAAgADAAAAAGAIAAoAAgADAAAAAGAIAAsAAgADAAAAAGAIAAwAAgADAAAAAGABAAkAAgADAAAAAFABAAoAAgADAAAAAFABAAsAAgADAAAAAFABAAwAAgADAAAAAFACAA0AAgADAAAAAAADAA0AAgADAAAAAAAEAA0AAgADAAAAAAAFAA0AAgADAAAAAAAGAA0AAgADAAAAAAAHAA0AAgADAAAAAAAIAA0AAgACAAAAAAABAA0AAgACAAAAAFA=")
tile_set = SubResource("TileSet_exbt2")

[node name="Player" parent="." instance=ExtResource("3_2e3f2")]
position = Vector2(220, 495)

[node name="Game" parent="." node_paths=PackedStringArray("_player") instance=ExtResource("4_3kdfk")]
_player = NodePath("../Player")

[editable path="Game"]

================
File: src/scenes/levels/shield_level.tscn
================
[gd_scene load_steps=9 format=4 uid="uid://cecvrncy6m2o"]

[ext_resource type="PackedScene" uid="uid://c2yxxlxf0tepl" path="res://src/scenes/color_cell/color_cell.tscn" id="1_yv17d"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_yp4u1"]
[ext_resource type="PackedScene" uid="uid://g3pg4o7n73rs" path="res://src/scenes/player/player.tscn" id="3_mjwo6"]
[ext_resource type="PackedScene" uid="uid://7tnwhag4o701" path="res://src/scenes/abilities/shield_ability.tscn" id="4_yv17d"]
[ext_resource type="PackedScene" uid="uid://cr1n35atcossq" path="res://src/scenes/game/game.tscn" id="5_cu36e"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_auga2"]
resource_name = "background"
scenes/1/scene = ExtResource("1_yv17d")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_25ao4"]
texture = ExtResource("2_yp4u1")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
12:3/0 = 0
13:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
10:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
0:6/0 = 0
0:6/0/physics_layer_1/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_2/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
12:6/0 = 0
13:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0

[sub_resource type="TileSet" id="TileSet_ypc8m"]
tile_size = Vector2i(128, 128)
physics_layer_0/collision_layer = 4
physics_layer_0/collision_mask = 0
physics_layer_1/collision_layer = 16
physics_layer_1/collision_mask = 0
physics_layer_2/collision_layer = 32
physics_layer_2/collision_mask = 0
custom_data_layer_0/name = "walkable"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_25ao4")
sources/0 = SubResource("TileSetScenesCollectionSource_auga2")

[node name="ShieldLevel" type="Node2D"]

[node name="Background" type="TileMapLayer" parent="."]
use_parent_material = true
position = Vector2(-100, 48)
tile_map_data = PackedByteArray("AAABAAMAAgADAAAAAFABAAQAAgADAAAAAFABAAUAAgADAAAAAFABAAYAAgADAAAAAFABAAcAAgADAAAAAFABAAgAAgADAAAAAFAIAAMAAgADAAAAAGAIAAQAAgADAAAAAGAIAAUAAgADAAAAAGAIAAYAAgADAAAAAGAIAAcAAgADAAAAAGAIAAgAAgADAAAAAGAHAAIAAgADAAAAADAGAAIAAgADAAAAADAFAAIAAgADAAAAADAEAAIAAgADAAAAADADAAIAAgADAAAAADACAAIAAgADAAAAADABAAIAAgACAAAAADAIAAIAAgACAAAAAGAHAAMAAgADAAYAAAAGAAMAAgADAAYAAAAFAAMAAAAAAAAAAQAEAAMAAAAAAAAAAQADAAMAAAAAAAAAAQACAAMAAAAAAAAAAQACAAQAAAAAAAAAAQADAAQAAAAAAAAAAQAEAAQAAAAAAAAAAQAFAAQAAAAAAAAAAQAGAAQAAgADAAYAAAAHAAQAAgADAAYAAAAHAAUAAAAAAAAAAQAHAAYAAAAAAAAAAQAHAAcAAAAAAAAAAQAHAAgAAAAAAAAAAQAGAAgAAAAAAAAAAQAFAAgAAAAAAAAAAQAEAAgAAAAAAAAAAQADAAgAAAAAAAAAAQACAAgAAAAAAAAAAQACAAcAAAAAAAAAAQADAAcAAAAAAAAAAQAEAAcAAAAAAAAAAQAFAAcAAAAAAAAAAQAGAAcAAAAAAAAAAQAGAAYAAAAAAAAAAQAFAAYAAAAAAAAAAQAEAAYAAAAAAAAAAQADAAYAAAAAAAAAAQACAAYAAAAAAAAAAQACAAUAAAAAAAAAAQADAAUAAAAAAAAAAQAEAAUAAAAAAAAAAQAFAAUAAAAAAAAAAQAGAAUAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAgADAAYAAAACAAwAAgADAAYAAAADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAgADAAYAAAADAAwAAgADAAYAAAAEAAkAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAQAIAAkAAgADAAAAAGAIAAoAAgADAAAAAGAIAAsAAgADAAAAAGAIAAwAAgADAAAAAGABAAkAAgADAAAAAFABAAoAAgADAAAAAFABAAsAAgADAAAAAFABAAwAAgADAAAAAFACAA0AAgADAAAAAAADAA0AAgADAAAAAAAEAA0AAgADAAAAAAAFAA0AAgADAAAAAAAGAA0AAgADAAAAAAAHAA0AAgADAAAAAAAIAA0AAgACAAAAAAABAA0AAgACAAAAAFA=")
tile_set = SubResource("TileSet_ypc8m")

[node name="Player" parent="." instance=ExtResource("3_mjwo6")]
position = Vector2(220, 495)

[node name="ShieldAbility" parent="Player" instance=ExtResource("4_yv17d")]

[node name="Game" parent="." node_paths=PackedStringArray("_player") instance=ExtResource("5_cu36e")]
_player = NodePath("../Player")

[editable path="Game"]

================
File: src/scenes/main_menu/main_menu.gd.uid
================
uid://dfheq04haeixx

================
File: src/scenes/paint_fuel/paint_fuel.gd.uid
================
uid://bmf0arg6on21w

================
File: src/scenes/player/player.gd.uid
================
uid://cw8wv0jyp107p

================
File: src/scripts/input/keyboard_input_handler.gd
================
class_name AnalogInputHandler
extends RefCounted

var _move_up_message := InputMessage.new(InputMessage.InputType.MOVE_UP)
var _move_down_message := InputMessage.new(InputMessage.InputType.MOVE_DOWN)
var _move_left_message := InputMessage.new(InputMessage.InputType.MOVE_LEFT)
var _move_right_message := InputMessage.new(InputMessage.InputType.MOVE_RIGHT)

func handle(event: InputEvent) -> void:
	if event.is_action_pressed("up"):
		Pigeon.send(_move_up_message)
	if event.is_action_pressed("down"):
		Pigeon.send(_move_down_message)
	if event.is_action_pressed("left"):
		Pigeon.send(_move_left_message)
	if event.is_action_pressed("right"):
		Pigeon.send(_move_right_message)

================
File: src/scripts/input/keyboard_input_handler.gd.uid
================
uid://ddd6xvkdorxvu

================
File: src/scripts/input/touch_input_handler.gd.uid
================
uid://d28elekdki3jq

================
File: src/ui/hud/paint_fuel_status.gd.uid
================
uid://2utr8gill6w8

================
File: src/ui/hud/paint_progress.gd.uid
================
uid://eukf8m64vdkn

================
File: src/ui/lost_menu/lost_menu.gd.uid
================
uid://duohpocjraqgh

================
File: src/ui/win_menu/win_menu.gd.uid
================
uid://br23jm6g1dxqj

================
File: src/ui/theme.tres
================
[gd_resource type="Theme" format=3 uid="uid://h1cj2a45sxcq"]

[resource]

================
File: src/autoloads/input_manager/input_manager.tscn
================
[gd_scene load_steps=2 format=3 uid="uid://cl01fdo586j1o"]

[ext_resource type="Script" uid="uid://c8t1stldb6yuq" path="res://src/autoloads/input_manager/input_manager.gd" id="1_kxf73"]

[node name="InputManager" type="Node"]
script = ExtResource("1_kxf73")

================
File: src/autoloads/pigeon/message.gd
================
class_name Message
extends RefCounted

var event_id: String
var result: Variant

func _init(id: String) -> void:
	self.event_id = id

================
File: src/autoloads/pigeon/pigeon.gd
================
extends Node

@export var logs: bool

# <event_id: String,
# subscription_dictionary: Dictionary<subscriber: Object, subscription: MessageSubscription>>
@onready var subscription_dictionary: Dictionary = Dictionary()


func subscribe(event_id: String, subscriber: Object, method_to_call: Callable) -> void:
	var subscription := MessageSubscription.new(event_id, subscriber, method_to_call)
	_add_subscription(subscription)
	_log("Subscribed: Message '%s', Subscriber '%s'" % [event_id, subscriber])


func unsubscribe(event_id: String, subscriber: Object) -> void:
	if not subscription_dictionary.has(event_id):
		push_error("Unsubscribe failed: Message '%s' not found in subscription dictionary." % event_id)
		return

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	if not event_subscriptions.has(subscriber):
		push_error("Unsubscribe failed: Subscriber '%s' not found for event '%s'." % [subscriber, event_id])
		return

	event_subscriptions.erase(subscriber)
	_log("Unsubscribed: Message '%s', Subscriber '%s'" % [event_id, subscriber])

	if event_subscriptions.is_empty():
		subscription_dictionary.erase(event_id)
		_log("Removed event '%s' from subscription dictionary due to no subscribers." % event_id)


func send(event: Message) -> void:
	var event_id := event.event_id
	if not subscription_dictionary.has(event_id):
		push_warning("Broadcast failed: No subscribers found for event '%s'" % event_id)
		return

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	for subscriber: Object in event_subscriptions.keys():
		var existing_sub := event_subscriptions.get(subscriber) as MessageSubscription
		if not existing_sub.method_to_call.is_valid():
			event_subscriptions.erase(subscriber)
			_log("Removed invalid subscription: Message '%s', Subscriber '%s'" % [event_id, subscriber])
			continue

		existing_sub.method_to_call.call(event)
		_log("Sent event '%s' to subscriber '%s'" % [event_id, subscriber])


func send_to(subscriber: Object, event: Message) -> Variant:
	var event_id := event.event_id
	if not subscription_dictionary.has(event_id):
		push_warning("Send failed: No subscriptions found for event '%s'" % event_id)
		return null

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	if not event_subscriptions.has(subscriber):
		push_warning("Send failed: Subscriber '%s' not found for event '%s'." % [subscriber, event_id])
		return null

	var existing_sub := event_subscriptions.get(subscriber) as MessageSubscription
	if not existing_sub.method_to_call.is_valid():
		event_subscriptions.erase(subscriber)
		push_warning("Send failed: Invalid method_to_call for subscriber '%s' and event '%s'." % [subscriber, event_id])
		return null

	await existing_sub.method_to_call.call(event)
	_log("Sent event '%s' to specific subscriber '%s'" % [event_id, subscriber])
	return event.result


func _add_subscription(subscription: MessageSubscription) -> void:
	var event_id := subscription.event_id
	var subscriber := subscription.subscriber

	if not subscription_dictionary.has(event_id):
		subscription_dictionary[event_id] = Dictionary()
		print("Added new event '%s' to subscription dictionary." % event_id)

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	event_subscriptions[subscriber] = subscription
	_log("Added subscription: Message '%s', Subscriber '%s'" % [event_id, subscriber])


func _log(message: String) -> void:
	if logs:
		print(message)

================
File: src/autoloads/pigeon/pigeon.tscn
================
[gd_scene load_steps=2 format=3 uid="uid://dgbf7ms2miue0"]

[ext_resource type="Script" uid="uid://da0wbb8gkep1s" path="res://src/autoloads/pigeon/pigeon.gd" id="1_as07w"]

[node name="Pigeon" type="Node"]
script = ExtResource("1_as07w")
logs = null

================
File: src/autoloads/scene_manager/scene_manager.gd
================
extends Node

@export var _scenes: Array[Scene] = []

func _ready() -> void:
	Pigeon.subscribe(SwitchSceneMessage.ID, self, _on_switch_scene)

func _exit_tree() -> void:
	Pigeon.unsubscribe(SwitchSceneMessage.ID, self)

func _on_switch_scene(msg: SwitchSceneMessage) -> void:
	var new_scene: Scene = _scenes.filter(func(scene: Scene) -> bool: return scene.type == msg.scene_type).front()
	get_tree().change_scene_to_packed(new_scene.packed)

================
File: src/commands/spawn/spawn_command.gd
================
class_name SpawnCommand
extends BaseCommand

@export var scene_to_spawn: PackedScene

func execute() -> void:
	Pigeon.send(SpawnMessage.new(scene_to_spawn, SpawnMessage.Type.RANDOM_EMPTY))

================
File: src/descriptors/progress/progress_descriptor.gd
================
class_name ProgressDescriptor
extends Resource

@export var _progress: Array[ProgressItemDescriptor]

func get_progress() -> Array[ProgressItemDescriptor]:
	return _progress.duplicate()

================
File: src/descriptors/progress/progress_item_descriptor.gd
================
class_name ProgressItemDescriptor
extends Resource

@export_range(0, 1, 0.1) var progress_step: float
@export var preview: Texture2D
@export var command: BaseCommand

func activate() -> void:
	command.execute()

================
File: src/messages/player/move/player_stop_message.gd
================
class_name PlayerStopMessage
extends Message

const ID: String = "player_stop"

var cell: BaseCell

func _init(cell_: BaseCell) -> void:
	super (ID)
	self.cell = cell_

================
File: src/messages/level_initialize_message.gd
================
class_name LevelInitializeMessage
extends Message

const ID: String = "level_initialize"

var level_descriptor: ProgressDescriptor
var cells: Array[ColorCell]

func _init(level_descriptor_: ProgressDescriptor, cells_: Array[ColorCell]) -> void:
	super(ID)
	self.level_descriptor = level_descriptor_
	self.cells = cells_

================
File: src/messages/spawn_message.gd
================
class_name SpawnMessage
extends Message

enum Type {
	NONE,
	RANDOM_EMPTY
}

const ID: String = "spawn"

var scene_to_spawn: PackedScene
var type: Type

func _init(scene_to_spawn_: PackedScene, type_: Type) -> void:
	super(ID)
	self.scene_to_spawn = scene_to_spawn_
	self.type = type_

================
File: src/scenes/abilities/regenerate_on_painted/regenerate_on_painted_ability.tscn
================
[gd_scene load_steps=2 format=3 uid="uid://c0ryyltbeiocm"]

[ext_resource type="Script" uid="uid://bu7paet67jbei" path="res://src/scenes/abilities/regenerate_on_painted/regenerate_on_painted_ability.gd" id="1_hyh6w"]

[node name="RegenerateOnPaintedAbility" type="Node2D"]
script = ExtResource("1_hyh6w")
amount = 1

================
File: src/scenes/abilities/shield/shield_ability.gd
================
class_name ShieldAbility
extends Node2D

var is_active: bool = true

func _ready() -> void:
	# Добавляем себя в группу обработчиков урона
	add_to_group("damage_handlers")

# Больше не нужны _exit_tree и подписки на Pigeon!

func handle_damage(msg: DamageMessage) -> bool:
	if not is_active:
		return false # Щит неактивен, урон не обработан

	is_active = false
	# Тут можно добавить анимацию/звук щита
	_play_shield_effect()

	# Логика отскока врага
	var player: Node2D = get_owner() as Node2D
	if player and msg.source:
		# Нормаль направлена от центра щита (игрока) к врагу
		var bounce_normal: Vector2 = (msg.source.global_position - player.global_position).normalized()
		# Вызываем метод отскока напрямую, если он есть у врага
		if msg.source.has_method("apply_bounce"):
			msg.source.call("apply_bounce", bounce_normal)

	print("Shield blocked the damage!")
	return true # Урон успешно обработан

func _play_shield_effect() -> void:
	# Заглушка для будущих эффектов
	print("Shield activated!")

func reactivate_shield() -> void:
	"""Публичный метод для переактивации щита (может понадобиться для других механик)"""
	is_active = true

================
File: src/scenes/game/paint_progress_step.tscn
================
[gd_scene load_steps=3 format=3 uid="uid://de4v2vb7u8wdx"]

[ext_resource type="Script" uid="uid://bdaltdkhaqhot" path="res://src/scenes/game/paint_progress_step.gd" id="1_orgxf"]

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_br17r"]
resource_local_to_scene = true
load_path = "res://.godot/imported/icon.svg-218a8f2b3041327d8a5756f3a245f83b.ctex"

[node name="Step" type="ColorRect" node_paths=PackedStringArray("_preview")]
offset_right = 9.0
offset_bottom = 27.0
color = Color(6.73831e-06, 0.727129, 0.592083, 1)
script = ExtResource("1_orgxf")
_preview = NodePath("Preview")

[node name="Preview" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_right = 0.5
offset_left = -32.5
offset_top = -68.0
offset_right = 31.5
offset_bottom = -4.0
texture = SubResource("CompressedTexture2D_br17r")
expand_mode = 1
stretch_mode = 4

================
File: src/scenes/game_object_factory/game_object_factory.tscn
================
[gd_scene load_steps=2 format=3 uid="uid://co48jkiaid41k"]

[ext_resource type="Script" uid="uid://b8tbmn6hfvcl0" path="res://src/scenes/game_object_factory/game_object_factory.gd" id="1_qk6f6"]

[node name="GameObjectFactory" type="Node2D"]
script = ExtResource("1_qk6f6")

================
File: src/scripts/input/touch_input_handler.gd
================
class_name TouchInputHandler
extends RefCounted

var _move_up_message := InputMessage.new(InputMessage.InputType.MOVE_UP)
var _move_down_message := InputMessage.new(InputMessage.InputType.MOVE_DOWN)
var _move_left_message := InputMessage.new(InputMessage.InputType.MOVE_LEFT)
var _move_right_message := InputMessage.new(InputMessage.InputType.MOVE_RIGHT)

var swipe_start_position: Vector2 = Vector2()
var swipe_end_position: Vector2 = Vector2()
var swipe_threshold: float = 20.0

func handle(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		var touch_event: InputEventScreenTouch = event as InputEventScreenTouch
		if touch_event.is_pressed():
			swipe_start_position = touch_event.position

	if event is InputEventScreenDrag:
		var drag_event: InputEventScreenDrag = event as InputEventScreenDrag
		swipe_end_position = drag_event.position

	if event is InputEventScreenTouch:
		var release_event: InputEventScreenTouch = event as InputEventScreenTouch
		if not release_event.is_pressed():
			swipe_end_position = release_event.position
			_detect_swipe()

func _detect_swipe() -> void:
	var swipe_vector: Vector2 = swipe_end_position - swipe_start_position
	if swipe_vector.length() < swipe_threshold:
		return

	if abs(swipe_vector.x) > abs(swipe_vector.y):
		if swipe_vector.x > 0:
			Pigeon.send(_move_right_message)
		else:
			Pigeon.send(_move_left_message)
	else:
		if swipe_vector.y > 0:
			Pigeon.send(_move_down_message)
		else:
			Pigeon.send(_move_up_message)

================
File: src/ui/lost_menu/lost_menu.gd
================
extends CanvasLayer

@export var _restart_button: Button

func _ready() -> void:
	_restart_button.pressed.connect(_on_restart_pressed)
	Pigeon.subscribe(PlayerDieMessage.ID, self, _on_player_die)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerDieMessage.ID, self)

func _on_restart_pressed() -> void:
	get_tree().reload_current_scene()

func _on_player_die(_msg: PlayerDieMessage) -> void:
	show()

================
File: src/ui/win_menu/win_menu.gd
================
extends CanvasLayer

@export var _to_level_selection_button: Button

func _ready() -> void:
	_to_level_selection_button.pressed.connect(_on_to_level_selection_pressed)
	Pigeon.subscribe(LevelCompleteMessage.ID, self, _on_level_complete)

func _exit_tree() -> void:
	Pigeon.unsubscribe(LevelCompleteMessage.ID, self)

func _on_to_level_selection_pressed() -> void:
	Pigeon.send(SwitchSceneMessage.new(Scene.Type.LEVEL_SELECTION))

func _on_level_complete(_msg: LevelCompleteMessage) -> void:
	show()

================
File: src/autoloads/scene_manager/scene.gd
================
class_name Scene
extends Resource

enum Type {
	NONE,
	LEVEL_SELECTION,
	LEVEL_1,
	MAIN,
	LEVEL_2
}

@export var type: Type
@export var packed: PackedScene

================
File: src/messages/damage_message.gd
================
class_name DamageMessage
extends Message

const ID: String = "damage"

var source: Node2D # Узел, который нанес урон
var handled: bool = false # Флаг, показывающий, что урон был обработан (например, щитом)

func _init(source_: Node2D) -> void:
	super(ID)
	self.source = source_

================
File: src/scenes/game_object_factory/game_object_factory.gd
================
extends Node2D

@export var _container: Node2D

var _cells: Array[ColorCell]

func _ready() -> void:
	Pigeon.subscribe(LevelInitializeMessage.ID, self, _on_level_init)
	Pigeon.subscribe(SpawnMessage.ID, self, _on_spawn)

func _exit_tree() -> void:
	Pigeon.unsubscribe(LevelInitializeMessage.ID, self)
	Pigeon.unsubscribe(SpawnMessage.ID, self)

func _on_spawn(msg: SpawnMessage) -> void:
	match msg.type:
		SpawnMessage.Type.RANDOM_EMPTY:
			var random_cell: ColorCell = _cells.filter(func(c: ColorCell) -> bool: return c.get_occupants().size() == 0).pick_random()
			_spawn(msg.scene_to_spawn, random_cell.global_position)

func _on_level_init(msg: LevelInitializeMessage) -> void:
	_cells = msg.cells

func _spawn(scene: PackedScene, global_position_: Vector2) -> void:
	var instance: Node2D = scene.instantiate()
	instance.global_position = global_position_
	_container.call_deferred("add_child", instance)

================
File: src/scenes/level_selection_menu/level_selection_menu.tscn
================
[gd_scene load_steps=4 format=3 uid="uid://h3hoigpnb6nl"]

[ext_resource type="Script" uid="uid://ykpi76ru1lma" path="res://src/scenes/level_selection_menu/level_selection_menu.gd" id="1_feter"]
[ext_resource type="Script" uid="uid://daxefx2pqod4e" path="res://src/scenes/level_selection_menu/start_level_button.gd" id="2_15d2t"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mtx05"]

[node name="LevelSelectionMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_feter")

[node name="Panel" type="Panel" parent="."]
custom_minimum_size = Vector2(300, 300)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_mtx05")

[node name="VBoxContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="Level1" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Level 1"
script = ExtResource("2_15d2t")
_level_scene = 2

[node name="Level2" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Level 2"
script = ExtResource("2_15d2t")
_level_scene = 4

[node name="MainMenu" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Main Menu"
script = ExtResource("2_15d2t")
_level_scene = 3

================
File: src/scenes/main_menu/main_menu.gd
================
extends Control

func _on_play_button_pressed() -> void:
	Pigeon.send(SwitchSceneMessage.new(Scene.Type.LEVEL_SELECTION))

func _on_quit_button_pressed() -> void:
	get_tree().quit()

================
File: src/scenes/main_menu/main_menu.tscn
================
[gd_scene load_steps=3 format=3 uid="uid://b5n374cngqjsg"]

[ext_resource type="Script" uid="uid://dfheq04haeixx" path="res://src/scenes/main_menu/main_menu.gd" id="1_pi0hd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7ysh3"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_pi0hd")

[node name="Panel" type="Panel" parent="."]
custom_minimum_size = Vector2(300, 300)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_7ysh3")

[node name="VBoxContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="PlayButton" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Play"

[node name="SettingsButton" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Settings"

[node name="QuitButton" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Quit"

[connection signal="pressed" from="Panel/VBoxContainer/PlayButton" to="." method="_on_play_button_pressed"]
[connection signal="pressed" from="Panel/VBoxContainer/QuitButton" to="." method="_on_quit_button_pressed"]

================
File: src/scenes/paint_fuel/paint_fuel.gd
================
class_name PaintFuel
extends Area2D

@export var _amount: int

func _ready() -> void:
	area_entered.connect(_on_grab)

func _on_grab(_area: Area2D) -> void:
	Pigeon.send_to(_area, PaintFuelChangeMessage.new(PaintFuelChangeMessage.Type.ADD, _amount))
	Pigeon.send(PaintFuelGrabMessage.new())
	queue_free()

================
File: src/scenes/paint_fuel/paint_fuel.tscn
================
[gd_scene load_steps=5 format=3 uid="uid://nvgddiawx0lo"]

[ext_resource type="Script" uid="uid://bmf0arg6on21w" path="res://src/scenes/paint_fuel/paint_fuel.gd" id="1_htw5j"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="1_o1e2b"]

[sub_resource type="AtlasTexture" id="AtlasTexture_mo7l2"]
atlas = ExtResource("1_o1e2b")
region = Rect2(896, 1024, 128, 128)

[sub_resource type="CircleShape2D" id="CircleShape2D_l77uc"]
radius = 20.0

[node name="PaintFuel" type="Area2D"]
collision_layer = 8
script = ExtResource("1_htw5j")
_amount = 5

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_mo7l2")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_l77uc")

================
File: src/descriptors/progress/1_level_progress_descriptor.tres
================
[gd_resource type="Resource" script_class="ProgressDescriptor" load_steps=14 format=3 uid="uid://bxlbiejvgqhfy"]

[ext_resource type="PackedScene" uid="uid://s06ysgqubbxi" path="res://src/scenes/enemy/enemy.tscn" id="1_tshs3"]
[ext_resource type="Script" uid="uid://cig2og2t4c1ja" path="res://src/commands/spawn/spawn_command.gd" id="2_yqepg"]
[ext_resource type="Script" uid="uid://dgea3m7spwfkc" path="res://src/descriptors/progress/progress_item_descriptor.gd" id="3_iout8"]
[ext_resource type="Script" uid="uid://dafo2hucd5rft" path="res://src/descriptors/progress/progress_descriptor.gd" id="4_a3vfo"]

[sub_resource type="Resource" id="Resource_cm2ho"]
script = ExtResource("2_yqepg")
scene_to_spawn = ExtResource("1_tshs3")

[sub_resource type="AtlasTexture" id="AtlasTexture_o1idv"]
region = Rect2(0, 1024, 128, 128)

[sub_resource type="Resource" id="Resource_r7eig"]
script = ExtResource("3_iout8")
progress_step = 0.1
preview = SubResource("AtlasTexture_o1idv")
command = SubResource("Resource_cm2ho")

[sub_resource type="Resource" id="Resource_142of"]
script = ExtResource("2_yqepg")
scene_to_spawn = ExtResource("1_tshs3")

[sub_resource type="AtlasTexture" id="AtlasTexture_uie70"]
region = Rect2(0, 1024, 128, 128)

[sub_resource type="Resource" id="Resource_7xara"]
script = ExtResource("3_iout8")
progress_step = 0.5
preview = SubResource("AtlasTexture_uie70")
command = SubResource("Resource_142of")

[sub_resource type="Resource" id="Resource_l2cbm"]
script = ExtResource("2_yqepg")
scene_to_spawn = ExtResource("1_tshs3")

[sub_resource type="AtlasTexture" id="AtlasTexture_odtvo"]
region = Rect2(0, 1024, 128, 128)

[sub_resource type="Resource" id="Resource_thcaf"]
script = ExtResource("3_iout8")
progress_step = 0.9
preview = SubResource("AtlasTexture_odtvo")
command = SubResource("Resource_l2cbm")

[resource]
script = ExtResource("4_a3vfo")
_progress = Array[ExtResource("3_iout8")]([SubResource("Resource_r7eig"), SubResource("Resource_7xara"), SubResource("Resource_thcaf")])

================
File: src/ui/hud/paint_fuel_status.gd
================
extends CenterContainer

@export var _steps_container: HBoxContainer
@export var _step: ColorRect

var _steps: Array[ColorRect] = []

func _ready() -> void:
	Pigeon.subscribe(PlayerPaintFuelChangeMessage.ID, self, _on_player_paint_fuel_change)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerPaintFuelChangeMessage.ID, self)

func _on_player_paint_fuel_change(msg: PlayerPaintFuelChangeMessage) -> void:
	if _steps.size() == 0:
		for i in range(msg.current_fuel):
			var new_step: ColorRect = _step.duplicate()
			new_step.show()
			_steps_container.add_child(new_step)
			_steps.append(new_step)

	for step in _steps:
		step.color.a = 0
	for i in range(msg.current_fuel):
		_steps[i].color.a = 1

================
File: src/scenes/color_cell/color_cell.tscn
================
[gd_scene load_steps=5 format=3 uid="uid://c2yxxlxf0tepl"]

[ext_resource type="Script" uid="uid://c5wff3edrjm3m" path="res://src/scenes/color_cell/color_cell.gd" id="1_7e71i"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="1_lx3m7"]

[sub_resource type="AtlasTexture" id="AtlasTexture_p47pu"]
atlas = ExtResource("1_lx3m7")
region = Rect2(0, 0, 128, 128)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_tk3p8"]
size = Vector2(64, 64)

[node name="ColorCell" type="Area2D" groups=["interactable_cells"]]
collision_layer = 4
script = ExtResource("1_7e71i")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_p47pu")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.5, 1)
shape = SubResource("RectangleShape2D_tk3p8")

================
File: src/scenes/enemy/enemy.gd
================
class_name Enemy
extends CharacterBody2D

@export_range(0.0, 1500.0, 100.0) var _initial_velocity: float
@export_range(0.0, 1.0, 0.1) var _slow_factor: float
@export_range(0.0, 1.0, 0.1) var _angle_deviation: float

@export var _animation_player: AnimationPlayer
@export var _damage_area: Area2D

var _screen_size: Vector2 = Vector2()
var _boosted: bool = false

func _ready() -> void:
	_animation_player.play("spawn")
	await _animation_player.animation_finished

	_damage_area.area_entered.connect(_on_damage_area_entered)
	Pigeon.subscribe(PlayerMoveMessage.ID, self, _on_player_move)
	Pigeon.subscribe(PlayerStopMessage.ID, self, _on_player_stop)

	randomize()
	var initial_angle: float = randf_range(0, 2 * PI)
	velocity = Vector2(cos(initial_angle), sin(initial_angle)) * _initial_velocity * _slow_factor
	_screen_size = get_viewport_rect().size

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerMoveMessage.ID, self)
	Pigeon.unsubscribe(PlayerStopMessage.ID, self)

func _physics_process(delta: float) -> void:
	var motion: Vector2 = velocity * delta
	var collision: KinematicCollision2D = move_and_collide(motion)

	if collision:
		_bounce(collision)

func _bounce(collision: KinematicCollision2D) -> void:
	var normal: Vector2 = collision.get_normal()
	velocity = velocity.bounce(normal)
	_apply_random_deviation()

func _apply_random_deviation() -> void:
	var angle_deviation: float = randf_range(-_angle_deviation, _angle_deviation)
	velocity = velocity.rotated(angle_deviation)

func _on_damage_area_entered(area: Area2D) -> void:
	Pigeon.send_to(area, DamageMessage.new(self))

func _on_player_move(_msg: PlayerMoveMessage) -> void:
	velocity /= _slow_factor
	_boosted = true

func _on_player_stop(_msg: PlayerStopMessage) -> void:
	if not _boosted:
		return
	velocity *= _slow_factor
	_boosted = false

func apply_bounce(normal: Vector2) -> void:
	"""Публичный метод для отскока от щита"""
	velocity = normal * _initial_velocity
	_apply_random_deviation()

================
File: src/ui/hud/paint_progress.gd
================
extends ProgressBar

@export var _step_scene: PackedScene

var _progress_descriptor: ProgressDescriptor
var _steps: Array[PaintProgressStep] = []

func _ready() -> void:
	value_changed.connect(_on_value_change)
	Pigeon.subscribe(LevelInitializeMessage.ID, self, _on_level_init)
	Pigeon.subscribe(PaintProgressMessage.ID, self, _on_paint_progress)

func _exit_tree() -> void:
	Pigeon.unsubscribe(LevelInitializeMessage.ID, self)
	Pigeon.unsubscribe(PaintProgressMessage.ID, self)

func _on_level_init(msg: LevelInitializeMessage) -> void:
	_progress_descriptor = msg.level_descriptor
	for item: ProgressItemDescriptor in _progress_descriptor.get_progress():
		var step_position_x: float = size.x * item.progress_step
		var step_instance: PaintProgressStep = _step_scene.instantiate()
		var step_position_y: float = -step_instance.size.y
		step_instance.position = Vector2(step_position_x, step_position_y)
		step_instance.value = item.progress_step
		step_instance.set_preview(item.preview)
		step_instance.show()
		add_child(step_instance)
		_steps.append(step_instance)

func _on_value_change(new_value: float) -> void:
	if _steps.size() == 0:
		return
	var closest_step: PaintProgressStep = _steps.front()
	if new_value < closest_step.value:
		return
	var step_instance: PaintProgressStep = _steps.pop_front()
	step_instance.queue_free()

func _on_paint_progress(msg: PaintProgressMessage) -> void:
	value = msg.progress

================
File: src/scenes/color_cell/color_cell.gd
================
class_name ColorCell
extends BaseCell

var colored: bool

var _color_message: CellColorMessage

var _color: Color:
	get:
		return _color
	set(value):
		modulate = value
		_color = value
		colored = true
		Pigeon.send(_color_message)

func _ready() -> void:
	super()  # Вызываем _ready() родителя для подключения сигналов area_entered/area_exited
	_color_message = CellColorMessage.new(self)
	Pigeon.subscribe(ColorMessage.ID, self, _on_color)

func _exit_tree() -> void:
	Pigeon.unsubscribe(ColorMessage.ID, self)

func on_player_landed(player: Player) -> void:
	"""Вызывается когда игрок останавливается на этой клетке."""
	# Проверяем, есть ли у игрока краска и не мертв ли он
	if player.get_paint_fuel() <= 0 or player._dead:
		return

	# Пытаемся покрасить клетку
	var result: ColorMessageResult = await Pigeon.send_to(self, ColorMessage.new(Color.GREEN))
	if result and result.colored:
		player.decrease_paint_fuel(1)

func _on_color(msg: ColorMessage) -> void:
	var new_color: Color = msg.color
	if _color == new_color:
		msg.set_result(false)
		return
	_color = new_color
	msg.set_result(true)

================
File: src/scenes/enemy/enemy.tscn
================
[gd_scene load_steps=9 format=3 uid="uid://s06ysgqubbxi"]

[ext_resource type="Script" uid="uid://dnmi8ogm4aiek" path="res://src/scenes/enemy/enemy.gd" id="1_wiqym"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_2ws2h"]

[sub_resource type="Animation" id="Animation_vntpi"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_2yp82"]
resource_name = "spawn"
length = 0.4
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.25, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(1.1, 1.1), Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_3ajag"]
_data = {
&"RESET": SubResource("Animation_vntpi"),
&"spawn": SubResource("Animation_2yp82")
}

[sub_resource type="AtlasTexture" id="AtlasTexture_o4mct"]
atlas = ExtResource("2_2ws2h")
region = Rect2(0, 1024, 128, 128)

[sub_resource type="CircleShape2D" id="CircleShape2D_r16fb"]
radius = 40.0

[sub_resource type="CircleShape2D" id="CircleShape2D_sn3k5"]
radius = 38.0

[node name="Enemy" type="CharacterBody2D" node_paths=PackedStringArray("_animation_player", "_damage_area")]
collision_layer = 2
collision_mask = 21
motion_mode = 1
script = ExtResource("1_wiqym")
_initial_velocity = 1500.0
_slow_factor = 0.1
_angle_deviation = 0.1
_animation_player = NodePath("AnimationPlayer")
_damage_area = NodePath("Area2D")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_3ajag")
}
autoplay = "spawn"

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_o4mct")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_r16fb")
debug_color = Color(0.998354, 0, 0.144163, 0.42)

[node name="Area2D" type="Area2D" parent="."]
collision_layer = 2
monitorable = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_sn3k5")

================
File: src/autoloads/scene_manager/scene_manager.tscn
================
[gd_scene load_steps=11 format=3 uid="uid://xjvmyx5se7il"]

[ext_resource type="Script" uid="uid://ydma15bvdlbm" path="res://src/autoloads/scene_manager/scene_manager.gd" id="1_jnn01"]
[ext_resource type="PackedScene" uid="uid://h3hoigpnb6nl" path="res://src/scenes/level_selection_menu/level_selection_menu.tscn" id="2_iqnjs"]
[ext_resource type="Script" uid="uid://b3lrgj34fm1ew" path="res://src/autoloads/scene_manager/scene.gd" id="3_yfpyq"]
[ext_resource type="PackedScene" uid="uid://b5n374cngqjsg" path="res://src/scenes/main_menu/main_menu.tscn" id="5_mcrev"]
[ext_resource type="PackedScene" uid="uid://comjkx4qgwq0m" path="res://src/scenes/levels/blocking_boxes_level.tscn" id="6_rfddh"]
[ext_resource type="PackedScene" uid="uid://cssnyxomo54kq" path="res://src/scenes/levels/safe_boxes_level.tscn" id="6_wl8y1"]

[sub_resource type="Resource" id="Resource_ktx4d"]
script = ExtResource("3_yfpyq")
type = 3
packed = ExtResource("5_mcrev")

[sub_resource type="Resource" id="Resource_g1c7k"]
script = ExtResource("3_yfpyq")
type = 1
packed = ExtResource("2_iqnjs")

[sub_resource type="Resource" id="Resource_pxsx7"]
script = ExtResource("3_yfpyq")
type = 2
packed = ExtResource("6_wl8y1")

[sub_resource type="Resource" id="Resource_eerrj"]
script = ExtResource("3_yfpyq")
type = 4
packed = ExtResource("6_rfddh")

[node name="SceneManager" type="Node"]
script = ExtResource("1_jnn01")
_scenes = Array[ExtResource("3_yfpyq")]([SubResource("Resource_ktx4d"), SubResource("Resource_g1c7k"), SubResource("Resource_pxsx7"), SubResource("Resource_eerrj")])

================
File: src/scenes/player/player.gd
================
class_name Player
extends Area2D

@export var _initial_paint_fuel: int
@export var _max_paint_fuel: int
@export var _tile_size: int
@export var _ray: RayCast2D
@export var _animation_player: AnimationPlayer

const ANIMATION_DURATION: float = 0.1

var _paint_fuel: int = 0:
	get:
		return _paint_fuel
	set(value):
		_paint_fuel = min(max(value, 0), _max_paint_fuel)
		Pigeon.send(PlayerPaintFuelChangeMessage.new(_paint_fuel))

var _moving: bool = false:
	get:
		return _moving
	set(value):
		_moving = value
		match value:
			true:
				Pigeon.send(PlayerMoveMessage.new())
			false:
				var underneath_cell: BaseCell = _get_cell_underneath()
				Pigeon.send(PlayerStopMessage.new(underneath_cell))

var _move_tween: Tween
var _dead: bool

func construct() -> void:
	_paint_fuel = _initial_paint_fuel

func _ready() -> void:
	Pigeon.subscribe(InputMessage.ID, self, _on_input)
	Pigeon.subscribe(DamageMessage.ID, self, _on_damage)
	Pigeon.subscribe(PaintFuelChangeMessage.ID, self, _on_paint_fuel_change)

func _exit_tree() -> void:
	Pigeon.unsubscribe(InputMessage.ID, self)
	Pigeon.unsubscribe(DamageMessage.ID, self)
	Pigeon.unsubscribe(PaintFuelChangeMessage.ID, self)

func _on_input(msg: InputMessage) -> void:
	if _moving or _dead:
		return
	match msg.input:
		InputMessage.InputType.MOVE_UP:
			_move_async(Vector2.UP)
		InputMessage.InputType.MOVE_DOWN:
			_move_async(Vector2.DOWN)
		InputMessage.InputType.MOVE_LEFT:
			_move_async(Vector2.LEFT)
		InputMessage.InputType.MOVE_RIGHT:
			_move_async(Vector2.RIGHT)

func _on_damage(msg: DamageMessage) -> void:
	# Сначала предлагаем обработать урон дочерним способностям
	for handler in get_tree().get_nodes_in_group("damage_handlers"):
		if handler.has_method("handle_damage"):
			var handled: bool = handler.call("handle_damage", msg)
			if handled:
				# Способность обработала урон. Выходим.
				return

	# Если ни одна способность не обработала урон, тогда умираем.
	_die()

func _on_paint_fuel_change(msg: PaintFuelChangeMessage) -> void:
	match msg.change:
		PaintFuelChangeMessage.Type.ADD:
			_paint_fuel += msg.amount
		PaintFuelChangeMessage.Type.REMOVE:
			_paint_fuel -= msg.amount

	Pigeon.send(PlayerPaintFuelChangeMessage.new(_paint_fuel))

func _move_async(direction: Vector2) -> void:
	_ray.target_position = direction * _tile_size
	_ray.force_raycast_update()
	if _ray.is_colliding():
		return
	_move_tween = create_tween()
	_move_tween.tween_property(
		self,
		"position",
		position + direction * _tile_size,
		ANIMATION_DURATION
	).set_trans(Tween.TRANS_SINE)
	_moving = true
	await _move_tween.finished
	_moving = false

	# Уведомляем клетку о том, что игрок на неё приземлился
	var cell: BaseCell = _get_cell_underneath()
	print("cell: ", cell)
	if cell:
		cell.on_player_landed(self)

func decrease_paint_fuel(amount: int) -> void:
	"""Публичный метод для уменьшения краски. Используется клетками."""
	_paint_fuel -= amount

func get_paint_fuel() -> int:
	"""Публичный метод для получения количества краски. Используется клетками."""
	return _paint_fuel

func _die() -> void:
	_dead = true
	_move_tween.stop()
	_moving = false
	_animation_player.play("die")
	Pigeon.send(PlayerDieMessage.new())

func _get_cell_underneath() -> BaseCell:
	var cells := get_overlapping_areas()
	print("cells: ", cells)
	if cells.size() == 0:
		return
	if cells.size() > 1:
		push_warning("There are more then 1 cell underneath player.")
		return

	var cell := cells[0] as BaseCell
	print("cell: ", cell)
	return cell

================
File: src/scenes/player/player.tscn
================
[gd_scene load_steps=8 format=3 uid="uid://g3pg4o7n73rs"]

[ext_resource type="Script" uid="uid://cw8wv0jyp107p" path="res://src/scenes/player/player.gd" id="1_jqry8"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_vkxsp"]

[sub_resource type="Animation" id="Animation_0dooi"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:monitoring")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:monitorable")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/path = NodePath(".:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_0r3el"]
resource_name = "die"
length = 0.4
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.1, 1.1), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:monitoring")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath(".:monitorable")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_6lm2u"]
_data = {
&"RESET": SubResource("Animation_0dooi"),
&"die": SubResource("Animation_0r3el")
}

[sub_resource type="AtlasTexture" id="AtlasTexture_ejvq1"]
atlas = ExtResource("2_vkxsp")
region = Rect2(768, 1024, 128, 128)

[sub_resource type="CircleShape2D" id="CircleShape2D_h3lrw"]
radius = 39.0

[node name="Player" type="Area2D" node_paths=PackedStringArray("_ray", "_animation_player")]
collision_mask = 12
script = ExtResource("1_jqry8")
_initial_paint_fuel = 10
_max_paint_fuel = 10
_tile_size = 128
_ray = NodePath("PathChecker")
_animation_player = NodePath("AnimationPlayer")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_6lm2u")
}

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_ejvq1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_h3lrw")

[node name="PathChecker" type="RayCast2D" parent="."]
target_position = Vector2(0, 128)
collision_mask = 36

================
File: src/scenes/game/game.gd
================
extends Node2D

@export var _player: Player
@export var _paint_fuel_scene: PackedScene
@export var _level_descriptor: ProgressDescriptor

var _cells: Array[ColorCell] = []
var _progress: Array[ProgressItemDescriptor]

var _paint_progress: float:
	get:
		return _paint_progress
	set(value):
		_paint_progress = value
		var closest_progress_item: ProgressItemDescriptor = _progress.front() if _progress.size() > 0 else null
		if closest_progress_item and _paint_progress >= closest_progress_item.progress_step:
			closest_progress_item.activate()
			_progress.pop_front()
		Pigeon.send(PaintProgressMessage.new(_paint_progress))
		if _paint_progress >= 1:
			Pigeon.send(LevelCompleteMessage.new())

func _ready() -> void:
	_progress = _level_descriptor.get_progress()
	call_deferred("_get_cells")
	_player.construct()
	Pigeon.subscribe(PaintFuelGrabMessage.ID, self, _on_paint_fuel_grab)
	Pigeon.subscribe(CellColorMessage.ID, self, _on_cell_color)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PaintFuelGrabMessage.ID, self)
	Pigeon.unsubscribe(CellColorMessage.ID, self)

func _on_paint_fuel_grab(_msg: PaintFuelGrabMessage) -> void:
	Pigeon.send(SpawnMessage.new(_paint_fuel_scene, SpawnMessage.Type.RANDOM_EMPTY))

func _on_cell_color(msg: CellColorMessage) -> void:
	var cell: ColorCell = _cells.filter(func(c: ColorCell) -> bool: return c == msg.cell)[0]
	cell.colored = true

	var colored_cells: int = _cells.filter(func(c: ColorCell) -> bool: return c.colored).size()
	var total_cells: int = _cells.size()
	_paint_progress = float(colored_cells) / float(total_cells)
	print("paint_progress: %s" % _paint_progress)

func _get_cells() -> void:
	var cells := get_tree().get_nodes_in_group("interactable_cells")
	for cell: ColorCell in cells:
		_cells.append(cell)
	Pigeon.send(LevelInitializeMessage.new(_level_descriptor, _cells))

================
File: src/scenes/game/game.tscn
================
[gd_scene load_steps=13 format=3 uid="uid://cr1n35atcossq"]

[ext_resource type="Script" uid="uid://ccs8ekjsp4aii" path="res://src/scenes/game/game.gd" id="1_vsh6y"]
[ext_resource type="Resource" uid="uid://bxlbiejvgqhfy" path="res://src/descriptors/progress/1_level_progress_descriptor.tres" id="3_8le08"]
[ext_resource type="PackedScene" uid="uid://nvgddiawx0lo" path="res://src/scenes/paint_fuel/paint_fuel.tscn" id="6_er41n"]
[ext_resource type="Script" uid="uid://eukf8m64vdkn" path="res://src/ui/hud/paint_progress.gd" id="7_71ew8"]
[ext_resource type="PackedScene" uid="uid://co48jkiaid41k" path="res://src/scenes/game_object_factory/game_object_factory.tscn" id="8_jwcvm"]
[ext_resource type="Script" uid="uid://2utr8gill6w8" path="res://src/ui/hud/paint_fuel_status.gd" id="8_kc17e"]
[ext_resource type="PackedScene" uid="uid://de4v2vb7u8wdx" path="res://src/scenes/game/paint_progress_step.tscn" id="8_nw6jf"]
[ext_resource type="Script" uid="uid://duohpocjraqgh" path="res://src/ui/lost_menu/lost_menu.gd" id="11_7po7r"]
[ext_resource type="Script" uid="uid://br23jm6g1dxqj" path="res://src/ui/win_menu/win_menu.gd" id="12_10e28"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oi036"]
bg_color = Color(1, 0.256377, 0.633274, 1)
expand_margin_left = 10.0
expand_margin_top = 10.0
expand_margin_right = 10.0
expand_margin_bottom = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_se0ce"]
bg_color = Color(0.926053, 0.418972, 0.394875, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2yqcv"]
bg_color = Color(1.44392e-07, 0.724371, 0.234238, 1)

[node name="Game" type="Node2D"]
script = ExtResource("1_vsh6y")
_paint_fuel_scene = ExtResource("6_er41n")
_level_descriptor = ExtResource("3_8le08")

[node name="HUD" type="Node2D" parent="."]

[node name="PaintProgress" type="ProgressBar" parent="HUD"]
anchors_preset = -1
anchor_left = 0.140741
anchor_top = 0.113021
anchor_right = 0.860185
anchor_bottom = 0.113021
offset_left = 31.0
offset_top = 233.0
offset_right = 1050.0
offset_bottom = 260.0
max_value = 1.0
script = ExtResource("7_71ew8")
_step_scene = ExtResource("8_nw6jf")

[node name="Step" parent="HUD/PaintProgress" instance=ExtResource("8_nw6jf")]
visible = false
layout_mode = 0

[node name="PaintFuelStatus" type="CenterContainer" parent="HUD" node_paths=PackedStringArray("_steps_container", "_step")]
anchors_preset = -1
anchor_left = 0.146
anchor_top = 0.708
anchor_right = 0.146
anchor_bottom = 0.708
offset_left = 540.0
offset_top = 1881.0
offset_right = 550.0
offset_bottom = 1891.0
use_top_left = true
script = ExtResource("8_kc17e")
_steps_container = NodePath("PanelContainer/StepsContainer")
_step = NodePath("PanelContainer/StepsContainer/Step")

[node name="PanelContainer" type="PanelContainer" parent="HUD/PaintFuelStatus"]
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_oi036")

[node name="StepsContainer" type="HBoxContainer" parent="HUD/PaintFuelStatus/PanelContainer"]
layout_mode = 2
alignment = 1

[node name="Step" type="ColorRect" parent="HUD/PaintFuelStatus/PanelContainer/StepsContainer"]
visible = false
custom_minimum_size = Vector2(20, 25)
layout_mode = 2
color = Color(1, 1, 1, 0.447059)

[node name="PaintFuel" parent="." instance=ExtResource("6_er41n")]
position = Vector2(476, 498)

[node name="GameObjectFactory" parent="." node_paths=PackedStringArray("_container") instance=ExtResource("8_jwcvm")]
_container = NodePath("..")

[node name="LostMenu" type="CanvasLayer" parent="." node_paths=PackedStringArray("_restart_button")]
visible = false
script = ExtResource("11_7po7r")
_restart_button = NodePath("PanelContainer/MarginContainer/Restart")

[node name="PanelContainer" type="PanelContainer" parent="LostMenu"]
anchors_preset = -1
anchor_left = 0.144
anchor_top = 0.259
anchor_right = 0.854
anchor_bottom = 0.75
offset_left = 0.479996
offset_top = 0.720001
offset_right = -0.320007
offset_bottom = -0.00012207
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_se0ce")
metadata/_edit_use_anchors_ = true

[node name="MarginContainer" type="MarginContainer" parent="LostMenu/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_top = 150
theme_override_constants/margin_bottom = 150

[node name="Title" type="Label" parent="LostMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_font_sizes/font_size = 100
text = "You got sliced!"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Restart" type="Button" parent="LostMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8
theme_override_font_sizes/font_size = 70
text = "Restart"

[node name="WinMenu" type="CanvasLayer" parent="." node_paths=PackedStringArray("_to_level_selection_button")]
visible = false
script = ExtResource("12_10e28")
_to_level_selection_button = NodePath("PanelContainer/MarginContainer/BackToLevelSelection")

[node name="PanelContainer" type="PanelContainer" parent="WinMenu"]
anchors_preset = -1
anchor_left = 0.144
anchor_top = 0.259
anchor_right = 0.854
anchor_bottom = 0.75
offset_left = 0.479996
offset_top = 0.720001
offset_right = -0.320007
offset_bottom = -0.00012207
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_2yqcv")
metadata/_edit_use_anchors_ = true

[node name="MarginContainer" type="MarginContainer" parent="WinMenu/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_top = 150
theme_override_constants/margin_bottom = 150

[node name="Title" type="Label" parent="WinMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_font_sizes/font_size = 100
text = "You won!"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BackToLevelSelection" type="Button" parent="WinMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8
theme_override_font_sizes/font_size = 70
text = "To level selection"




================================================================
End of Codebase
================================================================
