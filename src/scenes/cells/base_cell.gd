class_name BaseCell
extends Area2D

@export var surface_type: SurfaceType.Type = SurfaceType.Type.LAND

var _occupants: Array[Node2D] = []

func _ready() -> void:
	area_entered.connect(_on_occupy)
	area_exited.connect(_on_leave)

func get_occupants() -> Array[Node2D]:
	return _occupants

func on_player_landed(player: Player) -> void:
	player.notify_landed_on_surface(surface_type)

func _on_occupy(occupant: Area2D) -> void:
	if _occupants.size() > 1:
		push_error("Too many occupants in cell: " + str(_occupants.size()))
	_occupants.append(occupant)

func _on_leave(occupant: Area2D) -> void:
	_occupants.erase(occupant)
