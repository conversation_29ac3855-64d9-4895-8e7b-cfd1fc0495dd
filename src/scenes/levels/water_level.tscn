[gd_scene load_steps=9 format=4 uid="uid://dq2kqkegmu2j5"]

[ext_resource type="PackedScene" uid="uid://c2yxxlxf0tepl" path="res://src/scenes/color_cell/color_cell.tscn" id="1_kb05q"]
[ext_resource type="PackedScene" uid="uid://bgay07uge8qm" path="res://src/scenes/water_cell/water_cell.tscn" id="2_mp5v4"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="3_ot0hl"]
[ext_resource type="PackedScene" uid="uid://g3pg4o7n73rs" path="res://src/scenes/player/player.tscn" id="4_1u1j7"]
[ext_resource type="PackedScene" uid="uid://cr1n35atcossq" path="res://src/scenes/game/game.tscn" id="5_jjbt2"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_xsj47"]
resource_name = "background"
scenes/1/scene = ExtResource("1_kb05q")
scenes/2/scene = ExtResource("2_mp5v4")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_yxbe0"]
texture = ExtResource("3_ot0hl")
texture_region_size = Vector2i(128, 128)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
3:0/0 = 0
3:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
8:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
6:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
10:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
12:3/0 = 0
13:3/0 = 0
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
10:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
0:6/0 = 0
0:6/0/physics_layer_1/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
6:6/0/physics_layer_2/polygon_0/points = PackedVector2Array(-64, -64, 64, -64, 64, 64, -64, 64)
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
12:6/0 = 0
13:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0

[sub_resource type="TileSet" id="TileSet_exbt2"]
tile_size = Vector2i(128, 128)
physics_layer_0/collision_layer = 4
physics_layer_0/collision_mask = 0
physics_layer_1/collision_layer = 16
physics_layer_1/collision_mask = 0
physics_layer_2/collision_layer = 32
physics_layer_2/collision_mask = 0
custom_data_layer_0/name = "walkable"
custom_data_layer_0/type = 1
sources/2 = SubResource("TileSetAtlasSource_yxbe0")
sources/0 = SubResource("TileSetScenesCollectionSource_xsj47")

[node name="WaterLevel" type="Node2D"]

[node name="Background" type="TileMapLayer" parent="."]
use_parent_material = true
position = Vector2(-100, 48)
tile_map_data = PackedByteArray("AAABAAMAAgADAAAAAFABAAQAAgADAAAAAFABAAUAAgADAAAAAFABAAYAAgADAAAAAFABAAcAAgADAAAAAFABAAgAAgADAAAAAFAIAAMAAgADAAAAAGAIAAQAAgADAAAAAGAIAAUAAgADAAAAAGAIAAYAAgADAAAAAGAIAAcAAgADAAAAAGAIAAgAAgADAAAAAGAHAAIAAgADAAAAADAGAAIAAgADAAAAADAFAAIAAgADAAAAADAEAAIAAgADAAAAADADAAIAAgADAAAAADACAAIAAgADAAAAADABAAIAAgACAAAAADAIAAIAAgACAAAAAGAHAAMAAAAAAAAAAQAGAAMAAAAAAAAAAQADAAMAAAAAAAAAAQACAAMAAAAAAAAAAQACAAQAAAAAAAAAAQADAAQAAAAAAAAAAQAGAAQAAAAAAAAAAQAHAAQAAAAAAAAAAQAHAAUAAAAAAAAAAQAHAAYAAAAAAAAAAQAHAAcAAAAAAAAAAQAHAAgAAAAAAAAAAQAGAAgAAAAAAAAAAQAFAAgAAAAAAAAAAQAEAAgAAAAAAAAAAQADAAgAAAAAAAAAAQACAAgAAAAAAAAAAQACAAcAAAAAAAAAAQADAAcAAAAAAAAAAQAEAAcAAAAAAAAAAQAFAAcAAAAAAAAAAQAGAAcAAAAAAAAAAQAGAAYAAAAAAAAAAQAFAAYAAAAAAAAAAQAEAAYAAAAAAAAAAQADAAYAAAAAAAAAAQACAAYAAAAAAAAAAQACAAUAAAAAAAAAAQADAAUAAAAAAAAAAQAGAAUAAAAAAAAAAQACAAkAAAAAAAAAAQACAAoAAAAAAAAAAQACAAsAAAAAAAAAAQACAAwAAAAAAAAAAQADAAkAAAAAAAAAAQADAAoAAAAAAAAAAQADAAsAAAAAAAAAAQADAAwAAAAAAAAAAQAEAAkAAAAAAAAAAQAEAAoAAAAAAAAAAQAEAAsAAAAAAAAAAQAEAAwAAAAAAAAAAQAFAAkAAAAAAAAAAQAFAAoAAAAAAAAAAQAFAAsAAAAAAAAAAQAFAAwAAAAAAAAAAQAGAAkAAAAAAAAAAQAGAAoAAAAAAAAAAQAGAAsAAAAAAAAAAQAGAAwAAAAAAAAAAQAHAAkAAAAAAAAAAQAHAAoAAAAAAAAAAQAHAAsAAAAAAAAAAQAHAAwAAAAAAAAAAQAIAAkAAgADAAAAAGAIAAoAAgADAAAAAGAIAAsAAgADAAAAAGAIAAwAAgADAAAAAGABAAkAAgADAAAAAFABAAoAAgADAAAAAFABAAsAAgADAAAAAFABAAwAAgADAAAAAFACAA0AAgADAAAAAAADAA0AAgADAAAAAAAEAA0AAgADAAAAAAAFAA0AAgADAAAAAAAGAA0AAgADAAAAAAAHAA0AAgADAAAAAAAIAA0AAgACAAAAAAABAA0AAgACAAAAAFAEAAMAAAAAAAAAAgAFAAMAAAAAAAAAAgAFAAQAAAAAAAAAAgAEAAQAAAAAAAAAAgAEAAUAAAAAAAAAAgAFAAUAAAAAAAAAAgA=")
tile_set = SubResource("TileSet_exbt2")

[node name="Player" parent="." instance=ExtResource("4_1u1j7")]
position = Vector2(220, 495)

[node name="Game" parent="." node_paths=PackedStringArray("_player") instance=ExtResource("5_jjbt2")]
_player = NodePath("../Player")

[node name="PaintFuel" parent="Game" index="1"]
position = Vector2(732, 624)

[editable path="Game"]
