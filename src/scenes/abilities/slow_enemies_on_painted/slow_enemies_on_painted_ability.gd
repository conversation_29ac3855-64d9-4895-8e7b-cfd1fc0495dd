class_name SlowEnemiesOnPaintedAbility
extends Node2D

@export var full_boost_multiplier: float = 1.0
@export var tactical_boost_multiplier: float = 0.3

func _ready() -> void:
	Pigeon.subscribe(PlayerMoveMessage.ID, self, _on_player_move)
	Pigeon.subscribe(PlayerStopMessage.ID, self, _on_player_stop)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerMoveMessage.ID, self)
	Pigeon.unsubscribe(PlayerStopMessage.ID, self)

func _on_player_move(msg: PlayerMoveMessage) -> void:
	var is_tactical_move: bool = _is_tactical_move(msg.start_cell, msg.destination_cell)

	var multiplier: float = tactical_boost_multiplier if is_tactical_move else full_boost_multiplier

	var enemies: Array[Node] = get_tree().get_nodes_in_group("enemies")
	for enemy in enemies:
		if enemy.has_method("boost_speed"):
			enemy.call("boost_speed", multiplier)

func _on_player_stop(_msg: PlayerStopMessage) -> void:
	var enemies: Array[Node] = get_tree().get_nodes_in_group("enemies")
	for enemy in enemies:
		if enemy.has_method("reset_speed"):
			enemy.call("reset_speed")

func _is_tactical_move(_start_cell: BaseCell, destination_cell: BaseCell) -> bool:
	if not (destination_cell is ColorCell):
		return false

	var destination_color_cell := destination_cell as ColorCell
	return destination_color_cell.colored
