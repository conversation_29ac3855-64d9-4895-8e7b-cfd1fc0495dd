class_name ShieldAbility
extends Node2D

var is_active: bool = true

func _ready() -> void:
	add_to_group("damage_handlers")


func handle_damage(msg: DamageMessage) -> bool:
	if not is_active:
		return false

	is_active = false
	_play_shield_effect()

	var player: Node2D = get_owner() as Node2D
	if player and msg.source:
		var bounce_normal: Vector2 = (msg.source.global_position - player.global_position).normalized()
		if msg.source.has_method("apply_bounce"):
			msg.source.call("apply_bounce", bounce_normal)

	print("Shield blocked the damage!")
	return true

func _play_shield_effect() -> void:
	print("Shield activated!")

func reactivate_shield() -> void:

	is_active = true
