extends Node2D

@export var amount: int

func _ready() -> void:
	Pigeon.subscribe(PlayerStopMessage.ID, self, _on_player_stop)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerStopMessage.ID, self)

func _on_player_stop(msg: PlayerStopMessage) -> void:
	if msg.cell is not ColorCell:
		return
	var color_cell: ColorCell = msg.cell
	if color_cell.colored:
		Pigeon.send(PaintFuelChangeMessage.new(PaintFuelChangeMessage.Type.ADD, amount))
