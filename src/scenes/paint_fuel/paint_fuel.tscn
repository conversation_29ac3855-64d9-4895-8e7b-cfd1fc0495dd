[gd_scene load_steps=5 format=3 uid="uid://nvgddiawx0lo"]

[ext_resource type="Script" uid="uid://bmf0arg6on21w" path="res://src/scenes/paint_fuel/paint_fuel.gd" id="1_htw5j"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="1_o1e2b"]

[sub_resource type="AtlasTexture" id="AtlasTexture_mo7l2"]
atlas = ExtResource("1_o1e2b")
region = Rect2(896, 1024, 128, 128)

[sub_resource type="CircleShape2D" id="CircleShape2D_l77uc"]
radius = 20.0

[node name="PaintFuel" type="Area2D"]
collision_layer = 8
script = ExtResource("1_htw5j")
_amount = 5

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_mo7l2")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_l77uc")
