[gd_scene load_steps=8 format=3 uid="uid://g3pg4o7n73rs"]

[ext_resource type="Script" uid="uid://cw8wv0jyp107p" path="res://src/scenes/player/player.gd" id="1_jqry8"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_vkxsp"]

[sub_resource type="Animation" id="Animation_0dooi"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:monitoring")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:monitorable")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/path = NodePath(".:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_0r3el"]
resource_name = "die"
length = 0.4
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.1, 1.1), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:monitoring")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath(".:monitorable")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_6lm2u"]
_data = {
&"RESET": SubResource("Animation_0dooi"),
&"die": SubResource("Animation_0r3el")
}

[sub_resource type="AtlasTexture" id="AtlasTexture_ejvq1"]
atlas = ExtResource("2_vkxsp")
region = Rect2(768, 1024, 128, 128)

[sub_resource type="CircleShape2D" id="CircleShape2D_h3lrw"]
radius = 39.0

[node name="Player" type="Area2D" node_paths=PackedStringArray("_wallChecker", "_cellChecker", "_animation_player")]
collision_mask = 12
script = ExtResource("1_jqry8")
_initial_paint_fuel = 10
_max_paint_fuel = 10
_tile_size = 128
_wallChecker = NodePath("WallChecker")
_cellChecker = NodePath("CellChecker")
_animation_player = NodePath("AnimationPlayer")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_6lm2u")
}

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_ejvq1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_h3lrw")

[node name="WallChecker" type="RayCast2D" parent="."]
target_position = Vector2(0, 128)
collision_mask = 36

[node name="CellChecker" type="RayCast2D" parent="."]
target_position = Vector2(0, 128)
collision_mask = 36
collide_with_areas = true
collide_with_bodies = false
