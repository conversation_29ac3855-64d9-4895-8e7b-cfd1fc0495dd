[gd_scene load_steps=5 format=3 uid="uid://bgay07uge8qm"]

[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="1_cc61j"]
[ext_resource type="Script" uid="uid://deokxpt1qccat" path="res://src/scenes/water_cell/water_cell.gd" id="1_vo27h"]

[sub_resource type="AtlasTexture" id="AtlasTexture_vo27h"]
atlas = ExtResource("1_cc61j")
region = Rect2(770, 770, 124, 124)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_46bcd"]
size = Vector2(64, 64)

[node name="WaterCell" type="Area2D"]
collision_layer = 4
script = ExtResource("1_vo27h")
surface_type = 1

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_vo27h")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.5, 1)
shape = SubResource("RectangleShape2D_46bcd")
