[gd_scene load_steps=9 format=3 uid="uid://s06ysgqubbxi"]

[ext_resource type="Script" uid="uid://dnmi8ogm4aiek" path="res://src/scenes/enemy/enemy.gd" id="1_wiqym"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="2_2ws2h"]

[sub_resource type="Animation" id="Animation_vntpi"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_2yp82"]
resource_name = "spawn"
length = 0.4
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.25, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(1.1, 1.1), Vector2(1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_3ajag"]
_data = {
&"RESET": SubResource("Animation_vntpi"),
&"spawn": SubResource("Animation_2yp82")
}

[sub_resource type="AtlasTexture" id="AtlasTexture_o4mct"]
atlas = ExtResource("2_2ws2h")
region = Rect2(0, 1024, 128, 128)

[sub_resource type="CircleShape2D" id="CircleShape2D_r16fb"]
radius = 40.0

[sub_resource type="CircleShape2D" id="CircleShape2D_sn3k5"]
radius = 38.0

[node name="Enemy" type="CharacterBody2D" node_paths=PackedStringArray("_animation_player", "_damage_area")]
collision_layer = 2
collision_mask = 21
motion_mode = 1
script = ExtResource("1_wiqym")
_initial_velocity = 1500.0
_slow_factor = 0.1
_angle_deviation = 0.1
_animation_player = NodePath("AnimationPlayer")
_damage_area = NodePath("Area2D")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_3ajag")
}
autoplay = "spawn"

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_o4mct")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_r16fb")
debug_color = Color(0.998354, 0, 0.144163, 0.42)

[node name="Area2D" type="Area2D" parent="."]
collision_layer = 2
monitorable = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_sn3k5")
