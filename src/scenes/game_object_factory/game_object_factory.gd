extends Node2D

@export var _container: Node2D

var _cells: Array[ColorCell]

func _ready() -> void:
	Pigeon.subscribe(LevelInitializeMessage.ID, self, _on_level_init)
	Pigeon.subscribe(SpawnMessage.ID, self, _on_spawn)

func _exit_tree() -> void:
	Pigeon.unsubscribe(LevelInitializeMessage.ID, self)
	Pigeon.unsubscribe(SpawnMessage.ID, self)

func _on_spawn(msg: SpawnMessage) -> void:
	match msg.type:
		SpawnMessage.Type.RANDOM_EMPTY:
			var random_cell: ColorCell = _cells.filter(func(c: ColorCell) -> bool: return c.get_occupants().size() == 0).pick_random()
			_spawn(msg.scene_to_spawn, random_cell.global_position)

func _on_level_init(msg: LevelInitializeMessage) -> void:
	_cells = msg.cells

func _spawn(scene: PackedScene, global_position_: Vector2) -> void:
	var instance: Node2D = scene.instantiate()
	instance.global_position = global_position_
	_container.call_deferred("add_child", instance)
