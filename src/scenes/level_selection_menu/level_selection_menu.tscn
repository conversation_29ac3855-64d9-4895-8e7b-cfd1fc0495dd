[gd_scene load_steps=4 format=3 uid="uid://h3hoigpnb6nl"]

[ext_resource type="Script" uid="uid://ykpi76ru1lma" path="res://src/scenes/level_selection_menu/level_selection_menu.gd" id="1_feter"]
[ext_resource type="Script" uid="uid://daxefx2pqod4e" path="res://src/scenes/level_selection_menu/start_level_button.gd" id="2_15d2t"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mtx05"]

[node name="LevelSelectionMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_feter")

[node name="Panel" type="Panel" parent="."]
custom_minimum_size = Vector2(300, 300)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_mtx05")

[node name="VBoxContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="Level1" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Level 1"
script = ExtResource("2_15d2t")
_level_scene = 2

[node name="Level2" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Level 2"
script = ExtResource("2_15d2t")
_level_scene = 4

[node name="MainMenu" type="Button" parent="Panel/VBoxContainer"]
custom_minimum_size = Vector2(0, 52.04)
layout_mode = 2
text = "Main Menu"
script = ExtResource("2_15d2t")
_level_scene = 3
