[gd_scene load_steps=5 format=3 uid="uid://c2yxxlxf0tepl"]

[ext_resource type="Script" uid="uid://c5wff3edrjm3m" path="res://src/scenes/color_cell/color_cell.gd" id="1_7e71i"]
[ext_resource type="Texture2D" uid="uid://buchwk5fjgsim" path="res://<EMAIL>" id="1_lx3m7"]

[sub_resource type="AtlasTexture" id="AtlasTexture_p47pu"]
atlas = ExtResource("1_lx3m7")
region = Rect2(0, 0, 128, 128)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_tk3p8"]
size = Vector2(64, 64)

[node name="ColorCell" type="Area2D" groups=["interactable_cells"]]
collision_layer = 4
script = ExtResource("1_7e71i")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = SubResource("AtlasTexture_p47pu")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.5, 1)
shape = SubResource("RectangleShape2D_tk3p8")
