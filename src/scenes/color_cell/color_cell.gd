class_name ColorCell
extends BaseCell

var colored: bool

var _color_message: CellColorMessage

var _color: Color:
	get:
		return _color
	set(value):
		modulate = value
		_color = value
		colored = true
		Pigeon.send(_color_message)

func _ready() -> void:
	super()
	_color_message = CellColorMessage.new(self)
	Pigeon.subscribe(ColorMessage.ID, self, _on_color)

func _exit_tree() -> void:
	Pigeon.unsubscribe(ColorMessage.ID, self)

func on_player_landed(player: Player) -> void:

	super.on_player_landed(player)

	if player.get_paint_fuel() <= 0 or player._dead:
		return

	var result: ColorMessageResult = await Pigeon.send_to(self, ColorMessage.new(Color.GREEN))
	if result and result.colored:
		player.decrease_paint_fuel(1)

func _on_color(msg: ColorMessage) -> void:
	var new_color: Color = msg.color
	if _color == new_color:
		msg.set_result(false)
		return
	_color = new_color
	msg.set_result(true)
