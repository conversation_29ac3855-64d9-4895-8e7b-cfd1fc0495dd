extends Node2D

@export var _player: Player
@export var _paint_fuel_scene: PackedScene
@export var _level_descriptor: ProgressDescriptor

var _cells: Array[ColorCell] = []
var _progress: Array[ProgressItemDescriptor]

var _paint_progress: float:
	get:
		return _paint_progress
	set(value):
		_paint_progress = value
		var closest_progress_item: ProgressItemDescriptor = _progress.front() if _progress.size() > 0 else null
		if closest_progress_item and _paint_progress >= closest_progress_item.progress_step:
			closest_progress_item.activate()
			_progress.pop_front()
		Pigeon.send(PaintProgressMessage.new(_paint_progress))
		if _paint_progress >= 1:
			Pigeon.send(LevelCompleteMessage.new())

func _ready() -> void:
	_progress = _level_descriptor.get_progress()
	call_deferred("_get_cells")
	_player.construct()
	Pigeon.subscribe(PaintFuelGrabMessage.ID, self, _on_paint_fuel_grab)
	Pigeon.subscribe(CellColorMessage.ID, self, _on_cell_color)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PaintFuelGrabMessage.ID, self)
	Pigeon.unsubscribe(CellColorMessage.ID, self)

func _on_paint_fuel_grab(_msg: PaintFuelGrabMessage) -> void:
	Pigeon.send(SpawnMessage.new(_paint_fuel_scene, SpawnMessage.Type.RANDOM_EMPTY))

func _on_cell_color(msg: CellColorMessage) -> void:
	var cell: ColorCell = _cells.filter(func(c: ColorCell) -> bool: return c == msg.cell)[0]
	cell.colored = true

	var colored_cells: int = _cells.filter(func(c: ColorCell) -> bool: return c.colored).size()
	var total_cells: int = _cells.size()
	_paint_progress = float(colored_cells) / float(total_cells)
	print("paint_progress: %s" % _paint_progress)

func _get_cells() -> void:
	var cells := get_tree().get_nodes_in_group("interactable_cells")
	for cell: ColorCell in cells:
		_cells.append(cell)
	Pigeon.send(LevelInitializeMessage.new(_level_descriptor, _cells))
