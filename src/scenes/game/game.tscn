[gd_scene load_steps=13 format=3 uid="uid://cr1n35atcossq"]

[ext_resource type="Script" uid="uid://ccs8ekjsp4aii" path="res://src/scenes/game/game.gd" id="1_vsh6y"]
[ext_resource type="Resource" uid="uid://bxlbiejvgqhfy" path="res://src/descriptors/progress/1_level_progress_descriptor.tres" id="3_8le08"]
[ext_resource type="PackedScene" uid="uid://nvgddiawx0lo" path="res://src/scenes/paint_fuel/paint_fuel.tscn" id="6_er41n"]
[ext_resource type="Script" uid="uid://eukf8m64vdkn" path="res://src/ui/hud/paint_progress.gd" id="7_71ew8"]
[ext_resource type="PackedScene" uid="uid://co48jkiaid41k" path="res://src/scenes/game_object_factory/game_object_factory.tscn" id="8_jwcvm"]
[ext_resource type="Script" uid="uid://2utr8gill6w8" path="res://src/ui/hud/paint_fuel_status.gd" id="8_kc17e"]
[ext_resource type="PackedScene" uid="uid://de4v2vb7u8wdx" path="res://src/scenes/game/paint_progress_step.tscn" id="8_nw6jf"]
[ext_resource type="Script" uid="uid://duohpocjraqgh" path="res://src/ui/lost_menu/lost_menu.gd" id="11_7po7r"]
[ext_resource type="Script" uid="uid://br23jm6g1dxqj" path="res://src/ui/win_menu/win_menu.gd" id="12_10e28"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_oi036"]
bg_color = Color(1, 0.256377, 0.633274, 1)
expand_margin_left = 10.0
expand_margin_top = 10.0
expand_margin_right = 10.0
expand_margin_bottom = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_se0ce"]
bg_color = Color(0.926053, 0.418972, 0.394875, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2yqcv"]
bg_color = Color(1.44392e-07, 0.724371, 0.234238, 1)

[node name="Game" type="Node2D"]
script = ExtResource("1_vsh6y")
_paint_fuel_scene = ExtResource("6_er41n")
_level_descriptor = ExtResource("3_8le08")

[node name="HUD" type="Node2D" parent="."]

[node name="PaintProgress" type="ProgressBar" parent="HUD"]
anchors_preset = -1
anchor_left = 0.140741
anchor_top = 0.113021
anchor_right = 0.860185
anchor_bottom = 0.113021
offset_left = 31.0
offset_top = 233.0
offset_right = 1050.0
offset_bottom = 260.0
max_value = 1.0
script = ExtResource("7_71ew8")
_step_scene = ExtResource("8_nw6jf")

[node name="Step" parent="HUD/PaintProgress" instance=ExtResource("8_nw6jf")]
visible = false
layout_mode = 0

[node name="PaintFuelStatus" type="CenterContainer" parent="HUD" node_paths=PackedStringArray("_steps_container", "_step")]
anchors_preset = -1
anchor_left = 0.146
anchor_top = 0.708
anchor_right = 0.146
anchor_bottom = 0.708
offset_left = 540.0
offset_top = 1881.0
offset_right = 550.0
offset_bottom = 1891.0
use_top_left = true
script = ExtResource("8_kc17e")
_steps_container = NodePath("PanelContainer/StepsContainer")
_step = NodePath("PanelContainer/StepsContainer/Step")

[node name="PanelContainer" type="PanelContainer" parent="HUD/PaintFuelStatus"]
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_oi036")

[node name="StepsContainer" type="HBoxContainer" parent="HUD/PaintFuelStatus/PanelContainer"]
layout_mode = 2
alignment = 1

[node name="Step" type="ColorRect" parent="HUD/PaintFuelStatus/PanelContainer/StepsContainer"]
visible = false
custom_minimum_size = Vector2(20, 25)
layout_mode = 2
color = Color(1, 1, 1, 0.447059)

[node name="PaintFuel" parent="." instance=ExtResource("6_er41n")]
position = Vector2(476, 498)

[node name="GameObjectFactory" parent="." node_paths=PackedStringArray("_container") instance=ExtResource("8_jwcvm")]
_container = NodePath("..")

[node name="LostMenu" type="CanvasLayer" parent="." node_paths=PackedStringArray("_restart_button")]
visible = false
script = ExtResource("11_7po7r")
_restart_button = NodePath("PanelContainer/MarginContainer/Restart")

[node name="PanelContainer" type="PanelContainer" parent="LostMenu"]
anchors_preset = -1
anchor_left = 0.144
anchor_top = 0.259
anchor_right = 0.854
anchor_bottom = 0.75
offset_left = 0.479996
offset_top = 0.720001
offset_right = -0.320007
offset_bottom = -0.00012207
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_se0ce")
metadata/_edit_use_anchors_ = true

[node name="MarginContainer" type="MarginContainer" parent="LostMenu/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_top = 150
theme_override_constants/margin_bottom = 150

[node name="Title" type="Label" parent="LostMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_font_sizes/font_size = 100
text = "You got sliced!"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Restart" type="Button" parent="LostMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8
theme_override_font_sizes/font_size = 70
text = "Restart"

[node name="WinMenu" type="CanvasLayer" parent="." node_paths=PackedStringArray("_to_level_selection_button")]
visible = false
script = ExtResource("12_10e28")
_to_level_selection_button = NodePath("PanelContainer/MarginContainer/BackToLevelSelection")

[node name="PanelContainer" type="PanelContainer" parent="WinMenu"]
anchors_preset = -1
anchor_left = 0.144
anchor_top = 0.259
anchor_right = 0.854
anchor_bottom = 0.75
offset_left = 0.479996
offset_top = 0.720001
offset_right = -0.320007
offset_bottom = -0.00012207
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_2yqcv")
metadata/_edit_use_anchors_ = true

[node name="MarginContainer" type="MarginContainer" parent="WinMenu/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_top = 150
theme_override_constants/margin_bottom = 150

[node name="Title" type="Label" parent="WinMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_font_sizes/font_size = 100
text = "You won!"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BackToLevelSelection" type="Button" parent="WinMenu/PanelContainer/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8
theme_override_font_sizes/font_size = 70
text = "To level selection"
