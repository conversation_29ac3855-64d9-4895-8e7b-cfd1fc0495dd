[gd_scene load_steps=3 format=3 uid="uid://de4v2vb7u8wdx"]

[ext_resource type="Script" uid="uid://bdaltdkhaqhot" path="res://src/scenes/game/paint_progress_step.gd" id="1_orgxf"]

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_br17r"]
resource_local_to_scene = true
load_path = "res://.godot/imported/icon.svg-218a8f2b3041327d8a5756f3a245f83b.ctex"

[node name="Step" type="ColorRect" node_paths=PackedStringArray("_preview")]
offset_right = 9.0
offset_bottom = 27.0
color = Color(6.73831e-06, 0.727129, 0.592083, 1)
script = ExtResource("1_orgxf")
_preview = NodePath("Preview")

[node name="Preview" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_right = 0.5
offset_left = -32.5
offset_top = -68.0
offset_right = 31.5
offset_bottom = -4.0
texture = SubResource("CompressedTexture2D_br17r")
expand_mode = 1
stretch_mode = 4
