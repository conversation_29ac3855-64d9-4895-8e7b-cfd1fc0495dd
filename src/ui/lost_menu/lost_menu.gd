extends CanvasLayer

@export var _restart_button: Button

func _ready() -> void:
	_restart_button.pressed.connect(_on_restart_pressed)
	Pigeon.subscribe(PlayerDieMessage.ID, self, _on_player_die)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerDieMessage.ID, self)

func _on_restart_pressed() -> void:
	get_tree().reload_current_scene()

func _on_player_die(_msg: PlayerDieMessage) -> void:
	show()
