extends CenterContainer

@export var _steps_container: HBoxContainer
@export var _step: ColorRect

var _steps: Array[ColorRect] = []

func _ready() -> void:
	Pigeon.subscribe(PlayerPaintFuelChangeMessage.ID, self, _on_player_paint_fuel_change)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerPaintFuelChangeMessage.ID, self)

func _on_player_paint_fuel_change(msg: PlayerPaintFuelChangeMessage) -> void:
	if _steps.size() == 0:
		for i in range(msg.current_fuel):
			var new_step: ColorRect = _step.duplicate()
			new_step.show()
			_steps_container.add_child(new_step)
			_steps.append(new_step)

	for step in _steps:
		step.color.a = 0
	for i in range(msg.current_fuel):
		_steps[i].color.a = 1
