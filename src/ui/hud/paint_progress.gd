extends ProgressBar

@export var _step_scene: PackedScene

var _progress_descriptor: ProgressDescriptor
var _steps: Array[PaintProgressStep] = []

func _ready() -> void:
	value_changed.connect(_on_value_change)
	Pigeon.subscribe(LevelInitializeMessage.ID, self, _on_level_init)
	Pigeon.subscribe(PaintProgressMessage.ID, self, _on_paint_progress)

func _exit_tree() -> void:
	Pigeon.unsubscribe(LevelInitializeMessage.ID, self)
	Pigeon.unsubscribe(PaintProgressMessage.ID, self)

func _on_level_init(msg: LevelInitializeMessage) -> void:
	_progress_descriptor = msg.level_descriptor
	for item: ProgressItemDescriptor in _progress_descriptor.get_progress():
		var step_position_x: float = size.x * item.progress_step
		var step_instance: PaintProgressStep = _step_scene.instantiate()
		var step_position_y: float = -step_instance.size.y
		step_instance.position = Vector2(step_position_x, step_position_y)
		step_instance.value = item.progress_step
		step_instance.set_preview(item.preview)
		step_instance.show()
		add_child(step_instance)
		_steps.append(step_instance)

func _on_value_change(new_value: float) -> void:
	if _steps.size() == 0:
		return
	var closest_step: PaintProgressStep = _steps.front()
	if new_value < closest_step.value:
		return
	var step_instance: PaintProgressStep = _steps.pop_front()
	step_instance.queue_free()

func _on_paint_progress(msg: PaintProgressMessage) -> void:
	value = msg.progress
