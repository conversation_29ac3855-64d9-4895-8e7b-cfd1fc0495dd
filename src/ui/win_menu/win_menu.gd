extends <PERSON>vas<PERSON><PERSON>er

@export var _to_level_selection_button: Button

func _ready() -> void:
	_to_level_selection_button.pressed.connect(_on_to_level_selection_pressed)
	Pigeon.subscribe(LevelCompleteMessage.ID, self, _on_level_complete)

func _exit_tree() -> void:
	Pigeon.unsubscribe(LevelCompleteMessage.ID, self)

func _on_to_level_selection_pressed() -> void:
	Pigeon.send(SwitchSceneMessage.new(Scene.Type.LEVEL_SELECTION))

func _on_level_complete(_msg: LevelCompleteMessage) -> void:
	show()
