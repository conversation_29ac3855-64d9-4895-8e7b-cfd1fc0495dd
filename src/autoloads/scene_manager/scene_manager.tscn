[gd_scene load_steps=11 format=3 uid="uid://xjvmyx5se7il"]

[ext_resource type="Script" uid="uid://ydma15bvdlbm" path="res://src/autoloads/scene_manager/scene_manager.gd" id="1_jnn01"]
[ext_resource type="PackedScene" uid="uid://h3hoigpnb6nl" path="res://src/scenes/level_selection_menu/level_selection_menu.tscn" id="2_iqnjs"]
[ext_resource type="Script" uid="uid://b3lrgj34fm1ew" path="res://src/autoloads/scene_manager/scene.gd" id="3_yfpyq"]
[ext_resource type="PackedScene" uid="uid://b5n374cngqjsg" path="res://src/scenes/main_menu/main_menu.tscn" id="5_mcrev"]
[ext_resource type="PackedScene" uid="uid://comjkx4qgwq0m" path="res://src/scenes/levels/blocking_boxes_level.tscn" id="6_rfddh"]
[ext_resource type="PackedScene" uid="uid://cssnyxomo54kq" path="res://src/scenes/levels/safe_boxes_level.tscn" id="6_wl8y1"]

[sub_resource type="Resource" id="Resource_ktx4d"]
script = ExtResource("3_yfpyq")
type = 3
packed = ExtResource("5_mcrev")

[sub_resource type="Resource" id="Resource_g1c7k"]
script = ExtResource("3_yfpyq")
type = 1
packed = ExtResource("2_iqnjs")

[sub_resource type="Resource" id="Resource_pxsx7"]
script = ExtResource("3_yfpyq")
type = 2
packed = ExtResource("6_wl8y1")

[sub_resource type="Resource" id="Resource_eerrj"]
script = ExtResource("3_yfpyq")
type = 4
packed = ExtResource("6_rfddh")

[node name="SceneManager" type="Node"]
script = ExtResource("1_jnn01")
_scenes = Array[ExtResource("3_yfpyq")]([SubResource("Resource_ktx4d"), SubResource("Resource_g1c7k"), SubResource("Resource_pxsx7"), SubResource("Resource_eerrj")])
