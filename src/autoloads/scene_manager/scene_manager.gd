extends Node

@export var _scenes: Array[Scene] = []

func _ready() -> void:
	Pigeon.subscribe(SwitchSceneMessage.ID, self, _on_switch_scene)

func _exit_tree() -> void:
	Pigeon.unsubscribe(SwitchSceneMessage.ID, self)

func _on_switch_scene(msg: SwitchSceneMessage) -> void:
	var new_scene: Scene = _scenes.filter(func(scene: Scene) -> bool: return scene.type == msg.scene_type).front()
	get_tree().change_scene_to_packed(new_scene.packed)
