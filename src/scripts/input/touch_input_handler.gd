class_name TouchInput<PERSON>and<PERSON>
extends RefCounted

var _move_up_message := InputMessage.new(InputMessage.InputType.MOVE_UP)
var _move_down_message := InputMessage.new(InputMessage.InputType.MOVE_DOWN)
var _move_left_message := InputMessage.new(InputMessage.InputType.MOVE_LEFT)
var _move_right_message := InputMessage.new(InputMessage.InputType.MOVE_RIGHT)

var swipe_start_position: Vector2 = Vector2()
var swipe_end_position: Vector2 = Vector2()
var swipe_threshold: float = 20.0

func handle(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		var touch_event: InputEventScreenTouch = event as InputEventScreenTouch
		if touch_event.is_pressed():
			swipe_start_position = touch_event.position

	if event is InputEventScreenDrag:
		var drag_event: InputEventScreenDrag = event as InputEventScreenDrag
		swipe_end_position = drag_event.position

	if event is InputEventScreenTouch:
		var release_event: InputEventScreenTouch = event as InputEventScreenTouch
		if not release_event.is_pressed():
			swipe_end_position = release_event.position
			_detect_swipe()

func _detect_swipe() -> void:
	var swipe_vector: Vector2 = swipe_end_position - swipe_start_position
	if swipe_vector.length() < swipe_threshold:
		return

	if abs(swipe_vector.x) > abs(swipe_vector.y):
		if swipe_vector.x > 0:
			Pigeon.send(_move_right_message)
		else:
			Pigeon.send(_move_left_message)
	else:
		if swipe_vector.y > 0:
			Pigeon.send(_move_down_message)
		else:
			Pigeon.send(_move_up_message)
