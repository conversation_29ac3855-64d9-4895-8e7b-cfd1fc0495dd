class_name AnalogInputHandler
extends RefCounted

var _move_up_message := InputMessage.new(InputMessage.InputType.MOVE_UP)
var _move_down_message := InputMessage.new(InputMessage.InputType.MOVE_DOWN)
var _move_left_message := InputMessage.new(InputMessage.InputType.MOVE_LEFT)
var _move_right_message := InputMessage.new(InputMessage.InputType.MOVE_RIGHT)

func handle(event: InputEvent) -> void:
	if event.is_action_pressed("up"):
		Pigeon.send(_move_up_message)
	if event.is_action_pressed("down"):
		Pigeon.send(_move_down_message)
	if event.is_action_pressed("left"):
		Pigeon.send(_move_left_message)
	if event.is_action_pressed("right"):
		Pigeon.send(_move_right_message)
